// Cryptographically secure password generation utilities
// Optimized for performance and security

export interface PasswordOptions {
  length: number;
  includeNumbers: boolean;
  includeSymbols: boolean;
  mode: 'random' | 'memorable' | 'pronounceable';
  wordCount?: number;
  useFunnyWords?: boolean;
}

export interface PasswordStrength {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong' | 'excellent';
  feedback: string[];
  emoji: string;
  message: string;
}

// Character sets for secure random generation
const CHAR_SETS = {
  lowercase: 'abcdefghijklmnopqrstuvwxyz',
  uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  numbers: '0123456789',
  symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  ambiguous: 'il1Lo0O', // Characters to avoid for better readability
} as const;

// Curated word lists for memorable passwords
const WORD_LISTS = {
  funnyAdjectives: [
    'silly', 'goofy', 'wacky', 'bouncy', 'giggly', 'wobbly', 'dizzy', 'quirky',
    'zany', 'nutty', 'loopy', 'bonkers', 'kooky', 'batty', 'dotty', 'daft',
    'cheeky', 'bubbly', 'peppy', 'zippy', 'snappy', 'fizzy', 'jazzy', 'funky',
    'wiggly', 'jiggly', 'squiggly', 'ticklish', 'giggly', 'bubbly', 'wibbly', 'wobbly',
    'fluffy', 'puffy', 'scruffy', 'snuggly', 'cuddly', 'huggable', 'squeezable', 'lovable',
    'cheerful', 'playful', 'joyful', 'gleeful', 'blissful', 'delightful', 'wonderful', 'magical',
    'sparkly', 'twinkly', 'shimmery', 'glittery', 'dazzling', 'brilliant', 'radiant', 'luminous',
    'bouncy', 'springy', 'elastic', 'flexible', 'bendy', 'stretchy', 'squishy', 'smooshy',
    'perky', 'spunky', 'feisty', 'frisky', 'lively', 'spirited', 'energetic', 'dynamic',
    'whimsical', 'fanciful', 'dreamy', 'imaginative', 'creative', 'artistic', 'colorful', 'vibrant',
    'mischievous', 'sneaky', 'crafty', 'clever', 'witty', 'smart', 'brainy', 'genius',
    'adorable', 'precious', 'sweet', 'charming', 'lovely', 'beautiful', 'gorgeous', 'stunning'
  ],
  animals: [
    'penguin', 'llama', 'koala', 'panda', 'otter', 'sloth', 'hedgehog', 'ferret',
    'hamster', 'chinchilla', 'capybara', 'quokka', 'axolotl', 'platypus', 'narwhal', 'dolphin',
    'octopus', 'jellyfish', 'seahorse', 'starfish', 'butterfly', 'ladybug', 'dragonfly', 'firefly',
    'bunny', 'kitten', 'puppy', 'duckling', 'piglet', 'lamb', 'calf', 'foal',
    'squirrel', 'chipmunk', 'raccoon', 'badger', 'mole', 'vole', 'shrew', 'lemur',
    'monkey', 'baboon', 'orangutan', 'chimpanzee', 'gorilla', 'gibbon', 'macaque', 'tamarin',
    'elephant', 'hippo', 'rhino', 'giraffe', 'zebra', 'antelope', 'gazelle', 'impala',
    'kangaroo', 'wallaby', 'wombat', 'possum', 'bandicoot', 'echidna', 'dingo', 'tasmanian',
    'bear', 'wolf', 'fox', 'lynx', 'bobcat', 'cougar', 'jaguar', 'leopard',
    'turtle', 'tortoise', 'lizard', 'gecko', 'iguana', 'chameleon', 'salamander', 'newt',
    'frog', 'toad', 'tadpole', 'cricket', 'grasshopper', 'mantis', 'beetle', 'ant',
    'bee', 'wasp', 'hornet', 'moth', 'caterpillar', 'cocoon', 'chrysalis', 'spider',
    'crab', 'lobster', 'shrimp', 'clam', 'oyster', 'scallop', 'mussel', 'snail',
    'whale', 'shark', 'ray', 'eel', 'salmon', 'trout', 'bass', 'tuna'
  ],
  foods: [
    'waffle', 'pancake', 'muffin', 'cookie', 'pretzel', 'bagel', 'donut', 'croissant',
    'taco', 'burrito', 'pizza', 'pasta', 'noodle', 'ramen', 'sushi', 'dumpling',
    'cheese', 'butter', 'honey', 'maple', 'vanilla', 'chocolate', 'caramel', 'strawberry',
    'cupcake', 'brownie', 'fudge', 'truffle', 'bonbon', 'lollipop', 'gummy', 'jelly',
    'marshmallow', 'cotton', 'candy', 'taffy', 'caramel', 'toffee', 'brittle', 'nougat',
    'sandwich', 'burger', 'hotdog', 'sausage', 'bacon', 'ham', 'turkey', 'chicken',
    'salad', 'soup', 'stew', 'chili', 'curry', 'risotto', 'paella', 'goulash',
    'bread', 'toast', 'biscuit', 'scone', 'crumpet', 'muffin', 'roll', 'bun',
    'pie', 'tart', 'cake', 'pudding', 'custard', 'cream', 'mousse', 'souffle',
    'apple', 'banana', 'orange', 'grape', 'berry', 'cherry', 'peach', 'plum',
    'mango', 'kiwi', 'pineapple', 'coconut', 'lemon', 'lime', 'grapefruit', 'melon',
    'avocado', 'tomato', 'cucumber', 'carrot', 'celery', 'broccoli', 'spinach', 'lettuce',
    'potato', 'onion', 'garlic', 'pepper', 'mushroom', 'corn', 'peas', 'beans',
    'rice', 'quinoa', 'oats', 'barley', 'wheat', 'flour', 'sugar', 'salt',
    'milk', 'yogurt', 'cream', 'ice', 'smoothie', 'shake', 'juice', 'tea',
    'coffee', 'cocoa', 'latte', 'mocha', 'espresso', 'cappuccino', 'macchiato', 'frappuccino'
  ],
  actions: [
    'dancing', 'jumping', 'spinning', 'bouncing', 'wiggling', 'giggling', 'sneezing', 'yawning',
    'skipping', 'hopping', 'sliding', 'rolling', 'tumbling', 'flipping', 'twirling', 'prancing',
    'splashing', 'bubbling', 'sparkling', 'glowing', 'shimmering', 'twinkling', 'floating', 'soaring',
    'running', 'walking', 'jogging', 'sprinting', 'racing', 'chasing', 'escaping', 'fleeing',
    'climbing', 'crawling', 'creeping', 'sneaking', 'tiptoeing', 'marching', 'strutting', 'swaggering',
    'singing', 'humming', 'whistling', 'chirping', 'tweeting', 'chattering', 'babbling', 'mumbling',
    'laughing', 'chuckling', 'snickering', 'grinning', 'smiling', 'beaming', 'glowing', 'radiating',
    'playing', 'frolicking', 'romping', 'gamboling', 'cavorting', 'frisking', 'scampering', 'scurrying',
    'eating', 'munching', 'chomping', 'nibbling', 'gobbling', 'devouring', 'savoring', 'tasting',
    'sleeping', 'napping', 'dozing', 'snoozing', 'dreaming', 'resting', 'relaxing', 'lounging',
    'swimming', 'diving', 'surfing', 'paddling', 'wading', 'splashing', 'floating', 'drifting',
    'flying', 'gliding', 'swooping', 'diving', 'hovering', 'fluttering', 'flapping', 'soaring',
    'digging', 'burrowing', 'tunneling', 'excavating', 'scooping', 'scraping', 'scratching', 'clawing',
    'building', 'constructing', 'creating', 'making', 'crafting', 'designing', 'inventing', 'innovating',
    'exploring', 'discovering', 'investigating', 'searching', 'hunting', 'seeking', 'finding', 'locating',
    'celebrating', 'partying', 'cheering', 'applauding', 'clapping', 'whooping', 'hollering', 'shouting'
  ],
  objects: [
    'balloon', 'rainbow', 'cloud', 'star', 'moon', 'sun', 'flower', 'tree',
    'castle', 'bridge', 'tower', 'lighthouse', 'windmill', 'carousel', 'ferris', 'swing',
    'kite', 'umbrella', 'hat', 'scarf', 'mittens', 'boots', 'glasses', 'watch',
    'bicycle', 'tricycle', 'scooter', 'skateboard', 'rollerblade', 'wagon', 'sled', 'toboggan',
    'rocket', 'spaceship', 'airplane', 'helicopter', 'balloon', 'parachute', 'glider', 'drone',
    'boat', 'ship', 'yacht', 'canoe', 'kayak', 'raft', 'submarine', 'sailboat',
    'guitar', 'piano', 'violin', 'flute', 'trumpet', 'drum', 'xylophone', 'harmonica',
    'paintbrush', 'crayon', 'marker', 'pencil', 'pen', 'eraser', 'ruler', 'compass',
    'telescope', 'microscope', 'binoculars', 'magnifier', 'camera', 'projector', 'screen', 'monitor',
    'computer', 'laptop', 'tablet', 'phone', 'headphones', 'speaker', 'microphone', 'keyboard',
    'backpack', 'suitcase', 'briefcase', 'purse', 'wallet', 'pocket', 'pouch', 'bag',
    'pillow', 'blanket', 'sheet', 'towel', 'napkin', 'tissue', 'handkerchief', 'cloth',
    'candle', 'lantern', 'flashlight', 'lamp', 'bulb', 'torch', 'beacon', 'spotlight',
    'mirror', 'window', 'door', 'gate', 'fence', 'wall', 'roof', 'chimney',
    'garden', 'park', 'playground', 'sandbox', 'seesaw', 'slide', 'monkey', 'jungle',
    'treasure', 'chest', 'box', 'container', 'jar', 'bottle', 'cup', 'mug',
    'spoon', 'fork', 'knife', 'plate', 'bowl', 'tray', 'basket', 'bucket',
    'clock', 'timer', 'calendar', 'diary', 'journal', 'notebook', 'book', 'magazine'
  ],
  normalAdjectives: [
    'bright', 'calm', 'bold', 'wise', 'kind', 'fair', 'true', 'pure',
    'swift', 'strong', 'gentle', 'noble', 'brave', 'clever', 'quick', 'smart'
  ]
} as const;

/**
 * Generate cryptographically secure random bytes
 */
function getSecureRandomBytes(length: number): Uint8Array {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    return crypto.getRandomValues(new Uint8Array(length));
  }
  // Fallback for environments without Web Crypto API
  const array = new Uint8Array(length);
  for (let i = 0; i < length; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }
  return array;
}

/**
 * Get a cryptographically secure random integer within a range
 */
function getSecureRandomInt(min: number, max: number): number {
  const range = max - min;
  const bytesNeeded = Math.ceil(Math.log2(range) / 8);
  const maxValid = Math.floor(256 ** bytesNeeded / range) * range;
  
  let randomValue;
  do {
    const bytes = getSecureRandomBytes(bytesNeeded);
    randomValue = bytes.reduce((acc, byte, index) => acc + byte * (256 ** index), 0);
  } while (randomValue >= maxValid);
  
  return min + (randomValue % range);
}

/**
 * Select random element from array using secure random
 */
function getSecureRandomElement<T>(array: readonly T[]): T {
  const index = getSecureRandomInt(0, array.length);
  return array[index];
}

/**
 * Generate a random character-based password
 */
function generateRandomPassword(options: PasswordOptions): string {
  let charset = CHAR_SETS.lowercase + CHAR_SETS.uppercase;
  
  if (options.includeNumbers) {
    charset += CHAR_SETS.numbers;
  }
  
  if (options.includeSymbols) {
    charset += CHAR_SETS.symbols;
  }
  
  // Remove ambiguous characters for better readability
  const cleanCharset = charset.split('').filter(char => 
    !CHAR_SETS.ambiguous.includes(char)
  ).join('');
  
  let password = '';
  for (let i = 0; i < options.length; i++) {
    const randomIndex = getSecureRandomInt(0, cleanCharset.length);
    password += cleanCharset[randomIndex];
  }
  
  return password;
}

/**
 * Generate a memorable word-based password
 */
function generateMemorablePassword(options: PasswordOptions): string {
  const wordCount = options.wordCount || 3;
  const useFunny = options.useFunnyWords ?? true;
  const words: string[] = [];
  
  // Select word lists based on preferences
  const adjectiveList = useFunny ? WORD_LISTS.funnyAdjectives : WORD_LISTS.normalAdjectives;
  const nounLists = [WORD_LISTS.animals, WORD_LISTS.foods, WORD_LISTS.objects];
  
  // Generate words alternating between adjectives and nouns
  for (let i = 0; i < wordCount; i++) {
    let word: string;
    
    if (i === 0 || i % 2 === 0) {
      // Use adjective for first word and every other word
      word = getSecureRandomElement(adjectiveList);
    } else {
      // Use random noun category
      const nounList = getSecureRandomElement(nounLists);
      word = getSecureRandomElement(nounList);
    }
    
    // Capitalize first letter
    word = word.charAt(0).toUpperCase() + word.slice(1);
    words.push(word);
  }
  
  let password = words.join('-');
  
  // Add numbers if requested
  if (options.includeNumbers) {
    const numberCount = getSecureRandomInt(1, 4);
    for (let i = 0; i < numberCount; i++) {
      password += getSecureRandomInt(0, 10).toString();
    }
  }
  
  // Add symbols if requested
  if (options.includeSymbols) {
    const symbolCount = getSecureRandomInt(1, 3);
    for (let i = 0; i < symbolCount; i++) {
      password += getSecureRandomElement(CHAR_SETS.symbols.split(''));
    }
  }
  
  // Ensure we don't exceed the requested length
  if (password.length > options.length) {
    password = password.substring(0, options.length);
  }
  
  return password;
}

/**
 * Generate a pronounceable password
 */
function generatePronounceablePassword(options: PasswordOptions): string {
  const consonants = 'bcdfghjklmnpqrstvwxyz';
  const vowels = 'aeiou';
  let password = '';
  let useConsonant = getSecureRandomInt(0, 2) === 0;
  
  for (let i = 0; i < options.length; i++) {
    if (useConsonant) {
      password += getSecureRandomElement(consonants.split(''));
    } else {
      password += getSecureRandomElement(vowels.split(''));
    }
    useConsonant = !useConsonant;
    
    // Occasionally add numbers or symbols
    if (i > 0 && i % 4 === 0) {
      if (options.includeNumbers && getSecureRandomInt(0, 3) === 0) {
        password += getSecureRandomInt(0, 10).toString();
      } else if (options.includeSymbols && getSecureRandomInt(0, 4) === 0) {
        password += getSecureRandomElement(['!', '@', '#', '$', '%']);
      }
    }
  }
  
  // Capitalize first letter and some random letters
  password = password.charAt(0).toUpperCase() + password.slice(1);
  
  return password.substring(0, options.length);
}

/**
 * Main password generation function
 */
export function generatePassword(options: PasswordOptions): string {
  switch (options.mode) {
    case 'memorable':
      return generateMemorablePassword(options);
    case 'pronounceable':
      return generatePronounceablePassword(options);
    case 'random':
    default:
      return generateRandomPassword(options);
  }
}

/**
 * Calculate password strength with detailed analysis
 */
export function calculatePasswordStrength(password: string): PasswordStrength {
  let score = 0;
  const feedback: string[] = [];
  
  // Length scoring (0-40 points)
  const lengthScore = Math.min(password.length * 3, 40);
  score += lengthScore;
  
  if (password.length < 8) {
    feedback.push('Use at least 8 characters');
  } else if (password.length >= 12) {
    score += 10; // Bonus for good length
  }
  
  // Character variety scoring
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSymbols = /[^A-Za-z0-9]/.test(password);
  
  if (hasLower) score += 5;
  if (hasUpper) score += 5;
  if (hasNumbers) score += 10;
  if (hasSymbols) score += 15;
  
  const varietyCount = [hasLower, hasUpper, hasNumbers, hasSymbols].filter(Boolean).length;
  if (varietyCount >= 3) score += 10; // Bonus for variety
  
  // Pattern detection (penalties)
  if (/(.)\1{2,}/.test(password)) {
    score -= 10;
    feedback.push('Avoid repeating characters');
  }
  
  if (/123|abc|qwe/i.test(password)) {
    score -= 15;
    feedback.push('Avoid common sequences');
  }
  
  // Common words penalty
  const commonWords = ['password', 'admin', 'user', 'login', '1234'];
  if (commonWords.some(word => password.toLowerCase().includes(word))) {
    score -= 20;
    feedback.push('Avoid common words');
  }
  
  // Normalize score to 0-100
  score = Math.max(0, Math.min(100, score));
  
  // Determine level and message
  let level: PasswordStrength['level'];
  let emoji: string;
  let message: string;
  
  if (score < 20) {
    level = 'weak';
    emoji = '😟';
    message = 'Very weak - needs improvement!';
  } else if (score < 40) {
    level = 'fair';
    emoji = '😐';
    message = 'Fair - getting there...';
  } else if (score < 60) {
    level = 'good';
    emoji = '🙂';
    message = 'Good - pretty secure!';
  } else if (score < 80) {
    level = 'strong';
    emoji = '😊';
    message = 'Strong - very secure!';
  } else {
    level = 'excellent';
    emoji = '🤩';
    message = 'Excellent - Fort Knox level!';
  }
  
  return {
    score,
    level,
    feedback,
    emoji,
    message
  };
}

/**
 * Validate password generation options
 */
export function validatePasswordOptions(options: PasswordOptions): string[] {
  const errors: string[] = [];
  
  if (options.length < 4) {
    errors.push('Password length must be at least 4 characters');
  }
  
  if (options.length > 128) {
    errors.push('Password length cannot exceed 128 characters');
  }
  
  if (options.mode === 'memorable' && options.wordCount && options.wordCount < 1) {
    errors.push('Word count must be at least 1');
  }
  
  return errors;
}
