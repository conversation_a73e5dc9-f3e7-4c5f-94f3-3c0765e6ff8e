/**
 * Advanced security analysis and recommendations for password generation
 */

import { type PasswordStrength } from './passwordGenerator';

export interface SecurityRecommendation {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  action?: string;
  priority: number;
}

export interface SecurityAnalysis {
  overallScore: number;
  recommendations: SecurityRecommendation[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  summary: string;
}

// Common weak password patterns
const WEAK_PATTERNS = [
  /^(.)\1+$/, // All same character
  /^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i,
  /^(qwe|asd|zxc|qaz|wsx|edc)/i, // Keyboard patterns
  /^(password|admin|user|login|welcome|secret|test|demo)/i, // Common words
  /^(111|222|333|444|555|666|777|888|999|000)/i, // Repeated numbers
];

// Common dictionary words (simplified list)
const COMMON_WORDS = [
  'password', 'admin', 'user', 'login', 'welcome', 'secret', 'test', 'demo',
  'hello', 'world', 'computer', 'internet', 'email', 'website', 'account',
  'security', 'private', 'public', 'system', 'network', 'server', 'client',
  'database', 'application', 'software', 'hardware', 'technology', 'digital'
];

/**
 * Analyze password for common vulnerabilities
 */
export function analyzePasswordSecurity(password: string, strength: PasswordStrength): SecurityAnalysis {
  const recommendations: SecurityRecommendation[] = [];
  let overallScore = strength.score;

  // Check for weak patterns
  const hasWeakPattern = WEAK_PATTERNS.some(pattern => pattern.test(password));
  if (hasWeakPattern) {
    recommendations.push({
      id: 'weak-pattern',
      type: 'critical',
      title: 'Weak Pattern Detected',
      description: 'Your password contains predictable patterns that are easy to guess.',
      action: 'Generate a new password with more randomness',
      priority: 1
    });
    overallScore -= 20;
  }

  // Check for dictionary words
  const lowerPassword = password.toLowerCase();
  const containsCommonWord = COMMON_WORDS.some(word => lowerPassword.includes(word));
  if (containsCommonWord) {
    recommendations.push({
      id: 'dictionary-word',
      type: 'warning',
      title: 'Contains Common Words',
      description: 'Your password contains common dictionary words that reduce security.',
      action: 'Use memorable word combinations instead of common words',
      priority: 2
    });
    overallScore -= 10;
  }

  // Check length recommendations
  if (password.length < 12) {
    recommendations.push({
      id: 'short-length',
      type: 'warning',
      title: 'Password Too Short',
      description: 'Passwords shorter than 12 characters are more vulnerable to attacks.',
      action: 'Increase password length to at least 12 characters',
      priority: 3
    });
    overallScore -= 5;
  } else if (password.length >= 16) {
    recommendations.push({
      id: 'good-length',
      type: 'info',
      title: 'Excellent Length',
      description: 'Your password length provides strong protection against brute force attacks.',
      priority: 10
    });
    overallScore += 5;
  }

  // Check character variety
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSymbols = /[^A-Za-z0-9]/.test(password);
  const varietyCount = [hasLower, hasUpper, hasNumbers, hasSymbols].filter(Boolean).length;

  if (varietyCount < 3) {
    recommendations.push({
      id: 'low-variety',
      type: 'warning',
      title: 'Limited Character Variety',
      description: 'Using multiple character types (uppercase, lowercase, numbers, symbols) increases security.',
      action: 'Enable more character types in password generation',
      priority: 4
    });
  } else if (varietyCount === 4) {
    recommendations.push({
      id: 'good-variety',
      type: 'info',
      title: 'Excellent Character Variety',
      description: 'Your password uses all character types for maximum security.',
      priority: 11
    });
  }

  // Check for repeated characters
  const repeatedChars = /(.)\1{2,}/.test(password);
  if (repeatedChars) {
    recommendations.push({
      id: 'repeated-chars',
      type: 'warning',
      title: 'Repeated Characters',
      description: 'Consecutive repeated characters make passwords easier to guess.',
      action: 'Generate a new password without character repetition',
      priority: 5
    });
    overallScore -= 5;
  }

  // Normalize score
  overallScore = Math.max(0, Math.min(100, overallScore));

  // Determine risk level
  let riskLevel: SecurityAnalysis['riskLevel'];
  if (overallScore >= 80) riskLevel = 'low';
  else if (overallScore >= 60) riskLevel = 'medium';
  else if (overallScore >= 40) riskLevel = 'high';
  else riskLevel = 'critical';

  // Generate summary
  const criticalCount = recommendations.filter(r => r.type === 'critical').length;
  const warningCount = recommendations.filter(r => r.type === 'warning').length;
  
  let summary: string;
  if (criticalCount > 0) {
    summary = `Critical security issues detected. Immediate action recommended.`;
  } else if (warningCount > 0) {
    summary = `Some security improvements possible. Consider the recommendations.`;
  } else if (overallScore >= 80) {
    summary = `Excellent password security. Well done!`;
  } else {
    summary = `Good password security with room for improvement.`;
  }

  // Sort recommendations by priority
  recommendations.sort((a, b) => a.priority - b.priority);

  return {
    overallScore,
    recommendations,
    riskLevel,
    summary
  };
}

/**
 * Get security recommendations based on password generation options
 */
export function getGenerationRecommendations(options: {
  mode: string;
  length: number;
  includeNumbers: boolean;
  includeSymbols: boolean;
}): SecurityRecommendation[] {
  const recommendations: SecurityRecommendation[] = [];

  // Mode-specific recommendations
  if (options.mode === 'memorable') {
    recommendations.push({
      id: 'memorable-mode',
      type: 'info',
      title: 'Memorable Mode Active',
      description: 'Memorable passwords balance security with usability. Consider adding numbers and symbols for extra security.',
      priority: 20
    });
  } else if (options.mode === 'random') {
    recommendations.push({
      id: 'random-mode',
      type: 'info',
      title: 'Maximum Security Mode',
      description: 'Random passwords provide the highest security but may be harder to remember.',
      priority: 21
    });
  }

  // Length recommendations
  if (options.length < 8) {
    recommendations.push({
      id: 'increase-length',
      type: 'critical',
      title: 'Increase Password Length',
      description: 'Passwords should be at least 8 characters long for basic security.',
      action: 'Set length to at least 8 characters',
      priority: 1
    });
  } else if (options.length < 12) {
    recommendations.push({
      id: 'recommended-length',
      type: 'warning',
      title: 'Consider Longer Password',
      description: 'For better security, use at least 12 characters.',
      action: 'Increase length to 12+ characters',
      priority: 5
    });
  }

  // Character type recommendations
  if (!options.includeNumbers && !options.includeSymbols) {
    recommendations.push({
      id: 'add-complexity',
      type: 'warning',
      title: 'Add Character Complexity',
      description: 'Including numbers or symbols significantly improves password strength.',
      action: 'Enable numbers or symbols',
      priority: 3
    });
  }

  if (!options.includeSymbols && options.length < 16) {
    recommendations.push({
      id: 'consider-symbols',
      type: 'info',
      title: 'Consider Adding Symbols',
      description: 'Symbols provide additional security, especially for shorter passwords.',
      action: 'Enable symbol inclusion',
      priority: 15
    });
  }

  return recommendations.sort((a, b) => a.priority - b.priority);
}

/**
 * Check if password might be compromised (simplified check)
 * In a real application, this would check against known breach databases
 */
export function checkPasswordCompromise(password: string): {
  isCompromised: boolean;
  confidence: number;
  source?: string;
} {
  // Simplified check for obviously compromised passwords
  const obviouslyWeak = [
    'password', 'password123', '123456', 'qwerty', 'admin', 'letmein',
    'welcome', 'monkey', 'dragon', 'master', 'shadow', 'superman'
  ];

  const isObviouslyWeak = obviouslyWeak.some(weak => 
    password.toLowerCase().includes(weak.toLowerCase())
  );

  if (isObviouslyWeak) {
    return {
      isCompromised: true,
      confidence: 0.9,
      source: 'Common password database'
    };
  }

  // Check for simple patterns that are likely compromised
  if (password.length <= 6 || /^(.)\1+$/.test(password)) {
    return {
      isCompromised: true,
      confidence: 0.8,
      source: 'Pattern analysis'
    };
  }

  return {
    isCompromised: false,
    confidence: 0.1
  };
}

/**
 * Generate security tips based on current context
 */
export function getSecurityTips(): string[] {
  return [
    "Use a unique password for each account to prevent credential stuffing attacks.",
    "Consider using a password manager to store and generate strong passwords.",
    "Enable two-factor authentication (2FA) whenever possible for additional security.",
    "Regularly update passwords for critical accounts (every 90 days).",
    "Avoid using personal information like names, birthdays, or addresses in passwords.",
    "Be cautious of phishing attempts that try to steal your passwords.",
    "Use longer passwords (16+ characters) for high-value accounts.",
    "Memorable word combinations can be both secure and easy to remember.",
    "Never share passwords via email, text, or unsecured messaging.",
    "Log out of accounts when using shared or public computers."
  ];
}
