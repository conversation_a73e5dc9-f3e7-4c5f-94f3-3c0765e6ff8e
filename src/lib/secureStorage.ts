/**
 * Secure storage utilities for password history and user preferences
 * Uses encryption for sensitive data and provides secure cleanup
 */

export interface PasswordHistoryEntry {
  id: string;
  timestamp: number;
  strength: number;
  mode: string;
  length: number;
  hasNumbers: boolean;
  hasSymbols: boolean;
  // Note: We never store the actual password for security
}

export interface SecuritySettings {
  maxHistoryEntries: number;
  passwordExpirationDays: number;
  enableBreachCheck: boolean;
  enableSecurityNotifications: boolean;
}

const STORAGE_KEYS = {
  PASSWORD_HISTORY: 'sillypass_password_history',
  SECURITY_SETTINGS: 'sillypass_security_settings',
  LAST_GENERATION: 'sillypass_last_generation',
} as const;

const DEFAULT_SECURITY_SETTINGS: SecuritySettings = {
  maxHistoryEntries: 10,
  passwordExpirationDays: 90,
  enableBreachCheck: false, // Disabled by default for privacy
  enableSecurityNotifications: true,
};

/**
 * Simple encryption/decryption using Web Crypto API
 * Note: This is for basic obfuscation, not cryptographic security
 */
class SimpleEncryption {
  private static async getKey(): Promise<CryptoKey> {
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode('sillypass-storage-key-v1'),
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: new TextEncoder().encode('sillypass-salt'),
        iterations: 100000,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt']
    );
  }

  static async encrypt(data: string): Promise<string> {
    try {
      const key = await this.getKey();
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encodedData = new TextEncoder().encode(data);

      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        encodedData
      );

      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.warn('Encryption failed, storing as plain text:', error);
      return data; // Fallback to plain text
    }
  }

  static async decrypt(encryptedData: string): Promise<string> {
    try {
      const key = await this.getKey();
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.warn('Decryption failed, treating as plain text:', error);
      return encryptedData; // Fallback to plain text
    }
  }
}

/**
 * Secure storage manager
 */
export class SecureStorage {
  private static isAvailable(): boolean {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch {
      return false;
    }
  }

  private static async setItem(key: string, value: string, encrypt = false): Promise<void> {
    if (!this.isAvailable()) return;

    try {
      const dataToStore = encrypt ? await SimpleEncryption.encrypt(value) : value;
      localStorage.setItem(key, dataToStore);
    } catch (error) {
      console.warn(`Failed to store ${key}:`, error);
    }
  }

  private static async getItem(key: string, encrypted = false): Promise<string | null> {
    if (!this.isAvailable()) return null;

    try {
      const data = localStorage.getItem(key);
      if (!data) return null;

      return encrypted ? await SimpleEncryption.decrypt(data) : data;
    } catch (error) {
      console.warn(`Failed to retrieve ${key}:`, error);
      return null;
    }
  }

  private static removeItem(key: string): void {
    if (!this.isAvailable()) return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Failed to remove ${key}:`, error);
    }
  }

  // Password History Management
  static async getPasswordHistory(): Promise<PasswordHistoryEntry[]> {
    const data = await this.getItem(STORAGE_KEYS.PASSWORD_HISTORY, true);
    if (!data) return [];

    try {
      return JSON.parse(data);
    } catch {
      return [];
    }
  }

  static async addPasswordToHistory(entry: Omit<PasswordHistoryEntry, 'id' | 'timestamp'>): Promise<void> {
    const history = await this.getPasswordHistory();
    const settings = await this.getSecuritySettings();

    const newEntry: PasswordHistoryEntry = {
      ...entry,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
    };

    history.unshift(newEntry);

    // Limit history size
    if (history.length > settings.maxHistoryEntries) {
      history.splice(settings.maxHistoryEntries);
    }

    await this.setItem(STORAGE_KEYS.PASSWORD_HISTORY, JSON.stringify(history), true);
  }

  static async clearPasswordHistory(): Promise<void> {
    this.removeItem(STORAGE_KEYS.PASSWORD_HISTORY);
  }

  // Security Settings Management
  static async getSecuritySettings(): Promise<SecuritySettings> {
    const data = await this.getItem(STORAGE_KEYS.SECURITY_SETTINGS);
    if (!data) return DEFAULT_SECURITY_SETTINGS;

    try {
      return { ...DEFAULT_SECURITY_SETTINGS, ...JSON.parse(data) };
    } catch {
      return DEFAULT_SECURITY_SETTINGS;
    }
  }

  static async updateSecuritySettings(settings: Partial<SecuritySettings>): Promise<void> {
    const current = await this.getSecuritySettings();
    const updated = { ...current, ...settings };
    await this.setItem(STORAGE_KEYS.SECURITY_SETTINGS, JSON.stringify(updated));
  }

  // Last Generation Tracking
  static async getLastGenerationTime(): Promise<number | null> {
    const data = await this.getItem(STORAGE_KEYS.LAST_GENERATION);
    return data ? parseInt(data, 10) : null;
  }

  static async updateLastGenerationTime(): Promise<void> {
    await this.setItem(STORAGE_KEYS.LAST_GENERATION, Date.now().toString());
  }

  // Security Analysis
  static async getPasswordExpirationStatus(): Promise<{
    isExpired: boolean;
    daysUntilExpiration: number;
    lastGeneration: number | null;
  }> {
    const lastGeneration = await this.getLastGenerationTime();
    const settings = await this.getSecuritySettings();

    if (!lastGeneration) {
      return {
        isExpired: false,
        daysUntilExpiration: settings.passwordExpirationDays,
        lastGeneration: null,
      };
    }

    const daysSinceGeneration = Math.floor((Date.now() - lastGeneration) / (1000 * 60 * 60 * 24));
    const daysUntilExpiration = Math.max(0, settings.passwordExpirationDays - daysSinceGeneration);
    const isExpired = daysSinceGeneration >= settings.passwordExpirationDays;

    return {
      isExpired,
      daysUntilExpiration,
      lastGeneration,
    };
  }

  // Data cleanup for privacy
  static async clearAllData(): Promise<void> {
    Object.values(STORAGE_KEYS).forEach(key => {
      this.removeItem(key);
    });
  }

  // Export data for backup
  static async exportData(): Promise<string> {
    const history = await this.getPasswordHistory();
    const settings = await this.getSecuritySettings();
    const lastGeneration = await this.getLastGenerationTime();

    return JSON.stringify({
      version: '1.0',
      exportDate: new Date().toISOString(),
      history,
      settings,
      lastGeneration,
    }, null, 2);
  }
}
