import { useCallback, useRef } from 'react';
import { useTouchDevice } from './use-mobile';

export type HapticPattern = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' | 'selection';

interface HapticOptions {
  enabled?: boolean;
  fallbackToAudio?: boolean;
}

/**
 * Hook for providing haptic feedback on mobile devices
 * Includes fallback audio feedback for devices without haptic support
 */
export function useHapticFeedback(options: HapticOptions = {}) {
  const { enabled = true, fallbackToAudio = true } = options;
  const isTouch = useTouchDevice();
  const audioContextRef = useRef<AudioContext | null>(null);

  // Initialize audio context for fallback feedback
  const initAudioContext = useCallback(() => {
    if (!fallbackToAudio || audioContextRef.current) return;
    
    try {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }, [fallbackToAudio]);

  // Play audio feedback as fallback
  const playAudioFeedback = useCallback((frequency: number, duration: number) => {
    if (!fallbackToAudio || !audioContextRef.current) return;

    try {
      const oscillator = audioContextRef.current.createOscillator();
      const gainNode = audioContextRef.current.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContextRef.current.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.1, audioContextRef.current.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContextRef.current.currentTime + duration / 1000);

      oscillator.start(audioContextRef.current.currentTime);
      oscillator.stop(audioContextRef.current.currentTime + duration / 1000);
    } catch (error) {
      console.warn('Audio feedback failed:', error);
    }
  }, [fallbackToAudio]);

  // Get vibration pattern for different feedback types
  const getVibrationPattern = useCallback((pattern: HapticPattern): number | number[] => {
    switch (pattern) {
      case 'light':
        return 10;
      case 'medium':
        return 25;
      case 'heavy':
        return 50;
      case 'success':
        return [10, 50, 10];
      case 'warning':
        return [25, 100, 25];
      case 'error':
        return [50, 100, 50, 100, 50];
      case 'selection':
        return 5;
      default:
        return 25;
    }
  }, []);

  // Get audio feedback parameters for different patterns
  const getAudioParams = useCallback((pattern: HapticPattern): { frequency: number; duration: number } => {
    switch (pattern) {
      case 'light':
        return { frequency: 800, duration: 50 };
      case 'medium':
        return { frequency: 600, duration: 100 };
      case 'heavy':
        return { frequency: 400, duration: 150 };
      case 'success':
        return { frequency: 1000, duration: 200 };
      case 'warning':
        return { frequency: 500, duration: 300 };
      case 'error':
        return { frequency: 200, duration: 400 };
      case 'selection':
        return { frequency: 1200, duration: 30 };
      default:
        return { frequency: 600, duration: 100 };
    }
  }, []);

  // Main haptic feedback function
  const triggerHaptic = useCallback((pattern: HapticPattern = 'medium') => {
    if (!enabled) return;

    // Initialize audio context on first use
    if (fallbackToAudio && !audioContextRef.current) {
      initAudioContext();
    }

    // Try native haptic feedback first
    if ('vibrate' in navigator && isTouch) {
      try {
        const vibrationPattern = getVibrationPattern(pattern);
        const success = navigator.vibrate(vibrationPattern);
        
        if (success) {
          return; // Haptic feedback successful
        }
      } catch (error) {
        console.warn('Haptic feedback failed:', error);
      }
    }

    // Fallback to audio feedback
    if (fallbackToAudio) {
      const { frequency, duration } = getAudioParams(pattern);
      playAudioFeedback(frequency, duration);
    }
  }, [enabled, isTouch, fallbackToAudio, initAudioContext, getVibrationPattern, getAudioParams, playAudioFeedback]);

  // Convenience methods for common patterns
  const light = useCallback(() => triggerHaptic('light'), [triggerHaptic]);
  const medium = useCallback(() => triggerHaptic('medium'), [triggerHaptic]);
  const heavy = useCallback(() => triggerHaptic('heavy'), [triggerHaptic]);
  const success = useCallback(() => triggerHaptic('success'), [triggerHaptic]);
  const warning = useCallback(() => triggerHaptic('warning'), [triggerHaptic]);
  const error = useCallback(() => triggerHaptic('error'), [triggerHaptic]);
  const selection = useCallback(() => triggerHaptic('selection'), [triggerHaptic]);

  // Check if haptic feedback is supported
  const isSupported = useCallback(() => {
    return 'vibrate' in navigator || (fallbackToAudio && 'AudioContext' in window);
  }, [fallbackToAudio]);

  return {
    triggerHaptic,
    light,
    medium,
    heavy,
    success,
    warning,
    error,
    selection,
    isSupported,
    isTouch,
  };
}

/**
 * Hook for button press haptic feedback
 */
export function useButtonHaptic(pattern: HapticPattern = 'light') {
  const { triggerHaptic } = useHapticFeedback();

  return useCallback(() => {
    triggerHaptic(pattern);
  }, [triggerHaptic, pattern]);
}

/**
 * Hook for form interaction haptic feedback
 */
export function useFormHaptic() {
  const { triggerHaptic } = useHapticFeedback();

  return {
    onFocus: useCallback(() => triggerHaptic('selection'), [triggerHaptic]),
    onSuccess: useCallback(() => triggerHaptic('success'), [triggerHaptic]),
    onError: useCallback(() => triggerHaptic('error'), [triggerHaptic]),
    onChange: useCallback(() => triggerHaptic('light'), [triggerHaptic]),
  };
}
