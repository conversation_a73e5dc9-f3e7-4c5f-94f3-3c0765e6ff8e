import { useState, useCallback, useMemo } from 'react';
import {
  generatePassword,
  calculatePasswordStrength,
  validatePasswordOptions,
  type PasswordOptions,
  type PasswordStrength
} from '@/lib/passwordGenerator';
import { useToast } from '@/hooks/use-toast';
import { useScreenReaderAnnouncement } from '@/hooks/useKeyboardNavigation';
import { SecureStorage } from '@/lib/secureStorage';
import { checkPasswordCompromise } from '@/lib/securityAnalysis';

export interface UsePasswordGeneratorOptions {
  autoGenerate?: boolean;
  autoCopy?: boolean;
  onGenerate?: (password: string) => void;
  onCopy?: (password: string) => void;
}

export interface UsePasswordGeneratorReturn {
  // State
  password: string;
  isGenerating: boolean;
  copySuccess: boolean;
  strength: PasswordStrength | null;
  options: PasswordOptions;
  
  // Actions
  generateNewPassword: () => void;
  copyToClipboard: () => Promise<void>;
  updateOptions: (newOptions: Partial<PasswordOptions>) => void;
  clearPassword: () => void;
  
  // Computed
  hasPassword: boolean;
  isValid: boolean;
  validationErrors: string[];
}

const DEFAULT_OPTIONS: PasswordOptions = {
  length: 12,
  includeNumbers: true,
  includeSymbols: false,
  mode: 'random',
  wordCount: 3,
  useFunnyWords: true,
};

export function usePasswordGenerator(
  initialOptions: Partial<PasswordOptions> = {},
  hookOptions: UsePasswordGeneratorOptions = {}
): UsePasswordGeneratorReturn {
  const { toast } = useToast();
  const { announce } = useScreenReaderAnnouncement();
  const { autoGenerate = false, autoCopy = true, onGenerate, onCopy } = hookOptions;
  
  // State management
  const [password, setPassword] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [options, setOptions] = useState<PasswordOptions>({
    ...DEFAULT_OPTIONS,
    ...initialOptions,
  });
  
  // Memoized computations
  const validationErrors = useMemo(() => 
    validatePasswordOptions(options), 
    [options]
  );
  
  const isValid = useMemo(() => 
    validationErrors.length === 0, 
    [validationErrors]
  );
  
  const strength = useMemo(() => 
    password ? calculatePasswordStrength(password) : null,
    [password]
  );
  
  const hasPassword = useMemo(() => 
    password.length > 0, 
    [password]
  );
  
  // Auto-copy functionality with error handling
  const autoCopyToClipboard = useCallback(async (text: string) => {
    if (!autoCopy) return;

    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        console.warn('Clipboard API not available');
        return;
      }

      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);

      // Announce to screen readers
      announce('Password copied to clipboard', 'polite');

      onCopy?.(text);
    } catch (error) {
      console.warn('Auto copy failed:', error);
      // Don't show error toast for auto-copy failures, but log for debugging
      if (error instanceof Error) {
        console.warn('Auto copy error details:', error.message);
      }
    }
  }, [autoCopy, onCopy, announce]);
  
  // Manual copy with user feedback
  const copyToClipboard = useCallback(async () => {
    if (!password) {
      toast({
        title: "No Password",
        description: "Please generate a password first.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Check if clipboard API is available
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not supported');
      }

      await navigator.clipboard.writeText(password);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);

      toast({
        title: "✅ Copied!",
        description: "Password copied to clipboard.",
      });

      // Announce to screen readers
      announce('Password successfully copied to clipboard', 'assertive');

      onCopy?.(password);
    } catch (error) {
      console.error('Copy to clipboard failed:', error);

      // Provide fallback instructions
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      toast({
        title: "Copy Failed",
        description: `Unable to copy automatically. ${errorMessage.includes('not supported') ? 'Please select and copy the password manually.' : 'Please try again.'}`,
        variant: "destructive",
      });

      // Announce error to screen readers
      announce('Failed to copy password. Please copy manually.', 'assertive');
    }
  }, [password, toast, onCopy, announce]);
  
  // Password generation with optimizations
  const generateNewPassword = useCallback(() => {
    if (!isValid) {
      const errorMessage = validationErrors.join(', ');
      toast({
        title: "Invalid Options",
        description: errorMessage,
        variant: "destructive",
      });

      // Announce validation errors to screen readers
      announce(`Password generation failed: ${errorMessage}`, 'assertive');
      return;
    }

    setIsGenerating(true);

    // Add haptic feedback on mobile
    if ('vibrate' in navigator) {
      try {
        navigator.vibrate(50);
      } catch (error) {
        // Vibration not supported, continue silently
        console.debug('Vibration not supported:', error);
      }
    }

    // Use setTimeout to allow UI to update
    setTimeout(() => {
      try {
        const newPassword = generatePassword(options);

        if (!newPassword || newPassword.length === 0) {
          throw new Error('Generated password is empty');
        }

        setPassword(newPassword);

        // Check for compromised password
        const compromiseCheck = checkPasswordCompromise(newPassword);
        if (compromiseCheck.isCompromised) {
          toast({
            title: "⚠️ Security Warning",
            description: `This password may be compromised (${compromiseCheck.source}). Consider generating a new one.`,
            variant: "destructive",
          });
        }

        // Store in history (without the actual password for security)
        const passwordStrength = calculatePasswordStrength(newPassword);
        SecureStorage.addPasswordToHistory({
          strength: passwordStrength.score,
          mode: options.mode,
          length: newPassword.length,
          hasNumbers: options.includeNumbers,
          hasSymbols: options.includeSymbols,
        }).catch(error => {
          console.warn('Failed to save to history:', error);
        });

        // Update last generation time
        SecureStorage.updateLastGenerationTime().catch(error => {
          console.warn('Failed to update generation time:', error);
        });

        // Auto-copy if enabled
        autoCopyToClipboard(newPassword);

        // Announce successful generation to screen readers
        const modeText = options.mode === 'memorable' ? 'memorable' :
                        options.mode === 'pronounceable' ? 'pronounceable' : 'random';
        announce(`New ${modeText} password generated with ${newPassword.length} characters`, 'polite');

        // Callback for external handling
        onGenerate?.(newPassword);
      } catch (error) {
        console.error('Password generation failed:', error);

        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        toast({
          title: "Generation Failed",
          description: `Failed to generate password: ${errorMessage}. Please try again.`,
          variant: "destructive",
        });

        // Announce error to screen readers
        announce('Password generation failed. Please try again.', 'assertive');
      } finally {
        setIsGenerating(false);
      }
    }, 150); // Small delay for better UX
  }, [options, isValid, validationErrors, toast, autoCopyToClipboard, onGenerate, announce]);
  
  // Update options with validation
  const updateOptions = useCallback((newOptions: Partial<PasswordOptions>) => {
    setOptions(prev => {
      const updated = { ...prev, ...newOptions };
      
      // Auto-generate if enabled and options change
      if (autoGenerate && hasPassword) {
        // Debounce auto-generation
        setTimeout(() => {
          const newPassword = generatePassword(updated);
          setPassword(newPassword);
          autoCopyToClipboard(newPassword);
        }, 300);
      }
      
      return updated;
    });
  }, [autoGenerate, hasPassword, autoCopyToClipboard]);
  
  // Clear password
  const clearPassword = useCallback(() => {
    setPassword('');
    setCopySuccess(false);
  }, []);
  
  return {
    // State
    password,
    isGenerating,
    copySuccess,
    strength,
    options,
    
    // Actions
    generateNewPassword,
    copyToClipboard,
    updateOptions,
    clearPassword,
    
    // Computed
    hasPassword,
    isValid,
    validationErrors,
  };
}

// Utility hook for debounced option updates
export function useDebouncedPasswordOptions(
  initialOptions: Partial<PasswordOptions> = {},
  delay: number = 300
) {
  const [options, setOptions] = useState<PasswordOptions>({
    ...DEFAULT_OPTIONS,
    ...initialOptions,
  });
  const [debouncedOptions, setDebouncedOptions] = useState(options);
  
  const updateOptions = useCallback((newOptions: Partial<PasswordOptions>) => {
    const updated = { ...options, ...newOptions };
    setOptions(updated);
    
    // Debounce the update
    const timeoutId = setTimeout(() => {
      setDebouncedOptions(updated);
    }, delay);
    
    return () => clearTimeout(timeoutId);
  }, [options, delay]);
  
  return {
    options: debouncedOptions,
    updateOptions,
    immediateOptions: options,
  };
}
