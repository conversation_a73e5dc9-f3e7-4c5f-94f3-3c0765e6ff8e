import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { usePasswordGenerator } from '@/hooks/usePasswordGenerator';
import { useIsMobile, useDeviceType } from '@/hooks/use-mobile';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';
import {
  PasswordDisplay,
  PasswordStrengthMeter,
  PasswordSettings,
  GenerateButton,
  ParticleEffect
} from '@/components/password';

const Index = () => {
  const [showParticles, setShowParticles] = useState(false);
  const isMobile = useIsMobile();
  const deviceType = useDeviceType();
  const { success } = useHapticFeedback();

  // Use the optimized password generator hook
  const {
    password,
    isGenerating,
    copySuccess,
    strength,
    options,
    generateNewPassword,
    copyToClipboard,
    updateOptions,
    hasPassword
  } = usePasswordGenerator({
    length: isMobile ? 10 : 12, // Shorter passwords on mobile for better UX
    includeNumbers: true,
    includeSymbols: false,
    mode: 'memorable', // Start with memorable mode as per project requirements
    wordCount: isMobile ? 2 : 3, // Fewer words on mobile
    useFunnyWords: true
  }, {
    autoCopy: true,
    onGenerate: () => {
      setShowParticles(true);
      success(); // Haptic feedback on generation
      setTimeout(() => setShowParticles(false), isMobile ? 800 : 1000);
    }
  });



  return (
    <div className={`min-h-screen transition-all duration-1000 ${hasPassword ? 'bg-success-gradient' : 'bg-purple-gradient'}`}>
      {/* Mobile-optimized Header */}
      <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
        <h1 className={`font-bold ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
          <span className="text-primary">Silly</span>
          <span className="text-accent">Pass</span>
          <span className={`ml-2 ${isMobile ? 'text-xl' : 'text-2xl'}`}>🎉</span>
        </h1>
        {isMobile && (
          <p className="text-sm text-muted-foreground mt-2 px-4">
            Generate secure, memorable passwords instantly
          </p>
        )}
      </div>

      {/* Main Content with responsive sizing */}
      <div className={`mx-auto px-4 pb-8 ${isMobile ? 'max-w-sm' : 'max-w-md'}`}>
        {/* Main Generator Card with mobile optimizations */}
        <Card className={`bg-white/20 backdrop-blur-lg border-0 shadow-2xl relative overflow-hidden ${isMobile ? 'p-6' : 'p-8'}`}>
          {showParticles && <ParticleEffect particleCount={isMobile ? 5 : 8} />}

          <CardContent className={`p-0 ${isMobile ? 'space-y-4' : 'space-y-6'}`}>
            {/* Password Display */}
            <PasswordDisplay
              password={password}
              isGenerating={isGenerating}
              copySuccess={copySuccess}
              onCopy={copyToClipboard}
            />

            {/* Generate Button */}
            <GenerateButton
              onGenerate={generateNewPassword}
              isGenerating={isGenerating}
              mode={options.mode}
              size={isMobile ? 'md' : 'lg'}
            />

            {/* Password Settings */}
            <PasswordSettings
              options={options}
              onOptionsChange={updateOptions}
              disabled={isGenerating}
            />
          </CardContent>
        </Card>

        {/* Strength Indicator with mobile optimization */}
        {strength && (
          <Card className={`mt-4 bg-white/20 backdrop-blur-lg border-0 shadow-lg ${isMobile ? 'p-3' : 'p-4'}`}>
            <PasswordStrengthMeter
              strength={strength}
              showFeedback={!isMobile} // Hide detailed feedback on mobile to save space
            />
          </Card>
        )}
      </div>
    </div>
  );
};

export default Index;
