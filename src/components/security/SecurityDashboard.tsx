import React, { useState, useEffect, memo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  AlertTriangle, 
  Clock, 
  History, 
  Settings,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { SecureStorage, type PasswordHistoryEntry, type SecuritySettings } from '@/lib/secureStorage';
import { analyzePasswordSecurity, getSecurityTips, type SecurityAnalysis } from '@/lib/securityAnalysis';
import { type PasswordStrength } from '@/lib/passwordGenerator';
import { cn } from '@/lib/utils';

interface SecurityDashboardProps {
  currentPassword?: string;
  currentStrength?: PasswordStrength;
  className?: string;
}

export const SecurityDashboard = memo<SecurityDashboardProps>(({
  currentPassword,
  currentStrength,
  className
}) => {
  const [history, setHistory] = useState<PasswordHistoryEntry[]>([]);
  const [settings, setSettings] = useState<SecuritySettings | null>(null);
  const [expirationStatus, setExpirationStatus] = useState<{
    isExpired: boolean;
    daysUntilExpiration: number;
    lastGeneration: number | null;
  } | null>(null);
  const [securityAnalysis, setSecurityAnalysis] = useState<SecurityAnalysis | null>(null);
  const [securityTips] = useState(() => getSecurityTips().slice(0, 3));

  // Load security data
  useEffect(() => {
    const loadSecurityData = async () => {
      try {
        const [historyData, settingsData, expirationData] = await Promise.all([
          SecureStorage.getPasswordHistory(),
          SecureStorage.getSecuritySettings(),
          SecureStorage.getPasswordExpirationStatus()
        ]);

        setHistory(historyData);
        setSettings(settingsData);
        setExpirationStatus(expirationData);
      } catch (error) {
        console.error('Failed to load security data:', error);
      }
    };

    loadSecurityData();
  }, []);

  // Analyze current password security
  useEffect(() => {
    if (currentPassword && currentStrength) {
      const analysis = analyzePasswordSecurity(currentPassword, currentStrength);
      setSecurityAnalysis(analysis);
    } else {
      setSecurityAnalysis(null);
    }
  }, [currentPassword, currentStrength]);

  const getRiskLevelColor = (riskLevel: SecurityAnalysis['riskLevel']) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info': return <Info className="h-4 w-4 text-blue-500" />;
      default: return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const clearHistory = async () => {
    try {
      await SecureStorage.clearPasswordHistory();
      setHistory([]);
    } catch (error) {
      console.error('Failed to clear history:', error);
    }
  };

  if (!settings) {
    return (
      <Card className={cn("animate-pulse", className)}>
        <CardContent className="p-6">
          <div className="h-4 bg-muted rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Security Overview */}
      {securityAnalysis && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Security Score</span>
              <Badge className={getRiskLevelColor(securityAnalysis.riskLevel)}>
                {securityAnalysis.riskLevel.toUpperCase()}
              </Badge>
            </div>
            
            <Progress value={securityAnalysis.overallScore} className="h-2" />
            
            <p className="text-sm text-muted-foreground">
              {securityAnalysis.summary}
            </p>

            {securityAnalysis.recommendations.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Recommendations:</h4>
                {securityAnalysis.recommendations.slice(0, 3).map((rec) => (
                  <div key={rec.id} className="flex items-start gap-2 p-2 bg-muted/30 rounded-lg">
                    {getRecommendationIcon(rec.type)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{rec.title}</p>
                      <p className="text-xs text-muted-foreground">{rec.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Password Expiration */}
      {expirationStatus && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Password Expiration
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expirationStatus.isExpired ? (
              <div className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm font-medium">Password has expired</span>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Days until expiration</span>
                  <span className="text-sm font-medium">
                    {expirationStatus.daysUntilExpiration}
                  </span>
                </div>
                <Progress 
                  value={(expirationStatus.daysUntilExpiration / settings.passwordExpirationDays) * 100} 
                  className="h-2" 
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Password History */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Password History
            </div>
            {history.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearHistory}
                className="text-xs"
              >
                Clear History
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {history.length === 0 ? (
            <p className="text-sm text-muted-foreground">No password history available</p>
          ) : (
            <div className="space-y-2">
              {history.slice(0, 5).map((entry) => (
                <div key={entry.id} className="flex items-center justify-between p-2 bg-muted/30 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium capitalize">{entry.mode}</span>
                      <Badge variant="outline" className="text-xs">
                        {entry.length} chars
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {new Date(entry.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{entry.strength}%</div>
                    <div className="text-xs text-muted-foreground">strength</div>
                  </div>
                </div>
              ))}
              {history.length > 5 && (
                <p className="text-xs text-muted-foreground text-center">
                  +{history.length - 5} more entries
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Tips */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Security Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {securityTips.map((tip, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                <p className="text-sm text-muted-foreground">{tip}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

SecurityDashboard.displayName = 'SecurityDashboard';
