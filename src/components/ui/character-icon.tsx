import React from 'react';

interface CharacterIconProps {
  className?: string;
  size?: number;
}

export const CharacterIcon: React.FC<CharacterIconProps> = ({ 
  className = "", 
  size = 80 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 80 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      role="img"
      aria-label="SILLYPASS mascot character"
    >
      {/* Simple stick figure matching Figma design */}
      
      {/* Head - circle */}
      <circle
        cx="40"
        cy="20"
        r="12"
        stroke="currentColor"
        strokeWidth="3"
        fill="none"
      />
      
      {/* Eyes - dots */}
      <circle cx="36" cy="18" r="1.5" fill="currentColor" />
      <circle cx="44" cy="18" r="1.5" fill="currentColor" />
      
      {/* Smile */}
      <path
        d="M35 22 Q40 26 45 22"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
      />
      
      {/* Body - vertical line */}
      <line
        x1="40"
        y1="32"
        x2="40"
        y2="55"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Left arm - thumbs up */}
      <line
        x1="40"
        y1="40"
        x2="28"
        y2="35"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Thumb */}
      <line
        x1="28"
        y1="35"
        x2="25"
        y2="30"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Right arm */}
      <line
        x1="40"
        y1="40"
        x2="52"
        y2="45"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Left leg */}
      <line
        x1="40"
        y1="55"
        x2="32"
        y2="70"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
      
      {/* Right leg */}
      <line
        x1="40"
        y1="55"
        x2="48"
        y2="70"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
      />
    </svg>
  );
};