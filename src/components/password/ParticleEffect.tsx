import React, { memo } from 'react';
import { cn } from '@/lib/utils';

interface ParticleEffectProps {
  className?: string;
  particleCount?: number;
  emoji?: string;
}

export const ParticleEffect = memo<ParticleEffectProps>(({
  className,
  particleCount = 8,
  emoji = '✨'
}) => {
  return (
    <div className={cn("particle-container absolute inset-0 pointer-events-none", className)}>
      {Array.from({ length: particleCount }, (_, i) => (
        <div 
          key={i} 
          className="particle absolute top-1/2 left-1/2"
          style={{ 
            '--delay': `${i * 0.1}s`,
            left: `${50 + (i - particleCount / 2) * 8}%`,
            top: `${50 + ((i % 2) - 0.5) * 20}%`,
            animationDelay: `${i * 0.1}s`
          } as React.CSSProperties}
        >
          {emoji}
        </div>
      ))}
    </div>
  );
});

ParticleEffect.displayName = 'ParticleEffect';
