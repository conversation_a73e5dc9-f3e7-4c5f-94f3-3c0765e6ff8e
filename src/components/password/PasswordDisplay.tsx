import React, { memo, useState, useEffect } from 'react';
import { Copy, Check } from 'lucide-react';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface PasswordDisplayProps {
  password: string;
  isGenerating: boolean;
  copySuccess: boolean;
  onCopy: () => void;
  className?: string;
}

export const PasswordDisplay = memo<PasswordDisplayProps>(({
  password,
  isGenerating,
  copySuccess,
  onCopy,
  className
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const isMobile = useIsMobile();
  const { success, light } = useHapticFeedback();

  // Handle copy success animation
  useEffect(() => {
    if (copySuccess) {
      setShowSuccess(true);
      success(); // Haptic feedback for successful copy

      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [copySuccess, success]);

  const handleCopyClick = () => {
    if (isGenerating) return;
    light(); // Haptic feedback for button press
    onCopy();
  };

  if (!password) return null;

  return (
    <div className={cn(
      "password-reveal",
      isGenerating && "password-generating",
      showSuccess && "success-bounce",
      className
    )}>
      <div className="bg-white/90 rounded-2xl p-4 md:p-6 text-center shadow-lg relative overflow-hidden">
        {/* Success shimmer effect */}
        {showSuccess && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-success/20 to-transparent animate-pulse" />
        )}

        <div className="flex items-center justify-center gap-3 relative">
          <div
            className={cn(
              "text-lg md:text-xl font-bold text-gray-800 break-all flex-1 font-mono leading-relaxed",
              isMobile && "text-base leading-normal"
            )}
            role="textbox"
            aria-label={`Generated password: ${password}`}
            aria-readonly="true"
            tabIndex={0}
            style={{ userSelect: 'all' }}
          >
            {password}
          </div>
          <button
            onClick={handleCopyClick}
            onTouchStart={() => setIsPressed(true)}
            onTouchEnd={() => setIsPressed(false)}
            onMouseDown={() => setIsPressed(true)}
            onMouseUp={() => setIsPressed(false)}
            onMouseLeave={() => setIsPressed(false)}
            className={cn(
              "p-3 rounded-xl hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2",
              isMobile && "p-4 touch-feedback",
              isPressed && "scale-95 bg-gray-100",
              copySuccess && "bg-success/10"
            )}
            aria-label={copySuccess ? "Password copied to clipboard" : "Copy password to clipboard"}
            disabled={isGenerating}
            type="button"
          >
            {copySuccess ? (
              <Check className={cn("h-5 w-5 text-success transition-all duration-200", showSuccess && "scale-110")} aria-hidden="true" />
            ) : (
              <Copy className={cn("h-5 w-5 text-gray-600 transition-all duration-200", isPressed && "scale-110")} aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Enhanced success indicator */}
        {showSuccess && (
          <div
            className="absolute -top-3 -right-3 bg-success text-success-foreground px-3 py-1.5 rounded-full text-xs font-semibold shadow-lg animate-in fade-in-0 zoom-in-95 slide-in-from-top-2"
            role="status"
            aria-live="polite"
          >
            ✓ Copied
          </div>
        )}
      </div>
    </div>
  );
});

PasswordDisplay.displayName = 'PasswordDisplay';
