import React, { memo } from 'react';
import { Progress } from '@/components/ui/progress';
import { type PasswordStrength } from '@/lib/passwordGenerator';
import { cn } from '@/lib/utils';

interface PasswordStrengthMeterProps {
  strength: PasswordStrength;
  className?: string;
  showFeedback?: boolean;
}

export const PasswordStrengthMeter = memo<PasswordStrengthMeterProps>(({
  strength,
  className,
  showFeedback = false
}) => {
  const getProgressColor = (level: PasswordStrength['level']) => {
    switch (level) {
      case 'weak':
        return 'bg-destructive';
      case 'fair':
        return 'bg-yellow-500';
      case 'good':
        return 'bg-blue-500';
      case 'strong':
        return 'bg-green-500';
      case 'excellent':
        return 'bg-emerald-500';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div className={cn("text-center space-y-3", className)}>
      <div className="text-4xl mb-2 strength-pulse">{strength.emoji}</div>
      
      <div className="space-y-2">
        <Progress 
          value={strength.score} 
          className="h-3"
          // Custom progress color based on strength level
          style={{
            '--progress-background': getProgressColor(strength.level)
          } as React.CSSProperties}
        />
        
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <span className="capitalize">{strength.level}</span>
          <span>{strength.score}/100</span>
        </div>
      </div>
      
      <p className="text-sm font-medium text-muted-foreground">
        {strength.message}
      </p>
      
      {showFeedback && strength.feedback.length > 0 && (
        <div className="mt-3 p-3 bg-muted/50 rounded-lg">
          <p className="text-xs font-medium text-muted-foreground mb-1">
            Suggestions:
          </p>
          <ul className="text-xs text-muted-foreground space-y-1">
            {strength.feedback.map((feedback, index) => (
              <li key={index} className="flex items-start gap-1">
                <span className="text-yellow-500 mt-0.5">•</span>
                {feedback}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
});

PasswordStrengthMeter.displayName = 'PasswordStrengthMeter';
