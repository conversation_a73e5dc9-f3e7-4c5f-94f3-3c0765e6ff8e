import React, { memo, useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Loader2, Shu<PERSON>, Brain, Volume2 } from 'lucide-react';
import { type PasswordOptions } from '@/lib/passwordGenerator';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface GenerateButtonProps {
  onGenerate: () => void;
  isGenerating: boolean;
  disabled?: boolean;
  mode: PasswordOptions['mode'];
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const getModeIcon = (mode: PasswordOptions['mode']) => {
  switch (mode) {
    case 'memorable':
      return Brain;
    case 'pronounceable':
      return Volume2;
    case 'random':
    default:
      return Shuffle;
  }
};

const getModeText = (mode: PasswordOptions['mode']) => {
  switch (mode) {
    case 'memorable':
      return 'Generate Memorable Password';
    case 'pronounceable':
      return 'Generate Pronounceable Password';
    case 'random':
    default:
      return 'Generate Password';
  }
};

export const GenerateButton = memo<GenerateButtonProps>(({
  onGenerate,
  isGenerating,
  disabled = false,
  mode,
  className,
  size = 'lg'
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const Icon = getModeIcon(mode);
  const text = getModeText(mode);
  const isMobile = useIsMobile();
  const { success, medium } = useHapticFeedback();

  const sizeClasses = {
    sm: 'h-10 text-sm px-4',
    md: 'h-12 text-base px-6',
    lg: isMobile ? 'h-14 text-base px-6' : 'h-16 text-lg px-8'
  };

  const handleClick = () => {
    if (isGenerating || disabled) return;

    // Haptic feedback for button press
    medium();

    onGenerate();
  };

  const handleTouchStart = () => {
    if (!isGenerating && !disabled) {
      setIsPressed(true);
      // Light haptic feedback on touch start
      if (isMobile) {
        medium();
      }
    }
  };

  const handleTouchEnd = () => {
    setIsPressed(false);
  };

  // Add keyboard navigation support
  useKeyboardNavigation({
    onEnter: () => {
      if (!isGenerating && !disabled) {
        handleClick();
      }
    },
    onSpace: () => {
      if (!isGenerating && !disabled) {
        handleClick();
      }
    },
    disabled: isGenerating || disabled,
    preventDefault: false, // Let the button handle its own events
  });

  return (
    <Button
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      disabled={isGenerating || disabled}
      className={cn(
        "w-full btn-bounce bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg focus:ring-2 focus:ring-accent/50 focus:ring-offset-2 transition-all duration-200",
        isMobile && "touch-feedback active:scale-95",
        isPressed && "scale-95",
        isGenerating && "animate-pulse",
        sizeClasses[size],
        className
      )}
      aria-label={isGenerating ? 'Generating password, please wait' : `Generate ${mode} password`}
      aria-describedby="generate-button-description"
      type="button"
    >
      {isGenerating ? (
        <>
          <Loader2 className="animate-spin mr-2" aria-hidden="true" />
          <span className="animate-pulse">Generating...</span>
        </>
      ) : (
        <>
          <Icon className={cn("mr-2 transition-transform duration-200", isPressed && "scale-110")} aria-hidden="true" />
          <span className="font-semibold">{text}</span>
        </>
      )}
      <span id="generate-button-description" className="sr-only">
        Press Enter or Space to generate a new password
      </span>
    </Button>
  );
});

GenerateButton.displayName = 'GenerateButton';
