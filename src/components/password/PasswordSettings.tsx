import React, { memo, useCallback } from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type PasswordOptions } from '@/lib/passwordGenerator';
import { cn } from '@/lib/utils';

interface PasswordSettingsProps {
  options: PasswordOptions;
  onOptionsChange: (options: Partial<PasswordOptions>) => void;
  className?: string;
  disabled?: boolean;
}

export const PasswordSettings = memo<PasswordSettingsProps>(({
  options,
  onOptionsChange,
  className,
  disabled = false
}) => {
  const handleLengthChange = useCallback((value: number[]) => {
    onOptionsChange({ length: value[0] });
  }, [onOptionsChange]);

  const handleNumbersChange = useCallback((checked: boolean) => {
    onOptionsChange({ includeNumbers: checked });
  }, [onOptionsChange]);

  const handleSymbolsChange = useCallback((checked: boolean) => {
    onOptionsChange({ includeSymbols: checked });
  }, [onOptionsChange]);

  const handleModeChange = useCallback((mode: PasswordOptions['mode']) => {
    onOptionsChange({ mode });
  }, [onOptionsChange]);

  const handleWordCountChange = useCallback((value: number[]) => {
    onOptionsChange({ wordCount: value[0] });
  }, [onOptionsChange]);

  const handleFunnyWordsChange = useCallback((checked: boolean) => {
    onOptionsChange({ useFunnyWords: checked });
  }, [onOptionsChange]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Password Mode Selection */}
      <div className="space-y-2">
        <Label className="text-base font-semibold">Password Type</Label>
        <Select 
          value={options.mode} 
          onValueChange={handleModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select password type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="random">Random Characters</SelectItem>
            <SelectItem value="memorable">Memorable Words</SelectItem>
            <SelectItem value="pronounceable">Pronounceable</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Length/Word Count Slider */}
      {options.mode === 'memorable' ? (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-base font-semibold">Word Count</Label>
            <span className="text-sm font-medium">{options.wordCount || 3}</span>
          </div>
          <Slider
            value={[options.wordCount || 3]}
            onValueChange={handleWordCountChange}
            min={2}
            max={6}
            step={1}
            className="w-full"
            disabled={disabled}
          />
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-base font-semibold">Character Count</Label>
            <span className="text-sm font-medium">{options.length}</span>
          </div>
          <Slider
            value={[options.length]}
            onValueChange={handleLengthChange}
            min={4}
            max={50}
            step={1}
            className="w-full"
            disabled={disabled}
          />
        </div>
      )}

      {/* Memorable Words Options */}
      {options.mode === 'memorable' && (
        <div className="flex items-center justify-between">
          <Label className="text-base font-semibold">Use Funny Words</Label>
          <Switch
            checked={options.useFunnyWords ?? true}
            onCheckedChange={handleFunnyWordsChange}
            disabled={disabled}
          />
        </div>
      )}
      
      {/* Include Numbers */}
      <div className="flex items-center justify-between">
        <Label className="text-base font-semibold">Include Numbers</Label>
        <Switch
          checked={options.includeNumbers}
          onCheckedChange={handleNumbersChange}
          disabled={disabled}
        />
      </div>
      
      {/* Include Symbols */}
      <div className="flex items-center justify-between">
        <Label className="text-base font-semibold">Include Symbols</Label>
        <Switch
          checked={options.includeSymbols}
          onCheckedChange={handleSymbolsChange}
          disabled={disabled}
        />
      </div>

      {/* Mode-specific hints */}
      <div className="text-xs text-muted-foreground bg-muted/30 p-3 rounded-lg">
        {options.mode === 'random' && (
          <p>🎲 Generates completely random characters for maximum security</p>
        )}
        {options.mode === 'memorable' && (
          <p>🧠 Creates passwords with memorable word combinations like "Silly-Penguin-Dancing123"</p>
        )}
        {options.mode === 'pronounceable' && (
          <p>🗣️ Generates passwords that are easier to pronounce and remember</p>
        )}
      </div>
    </div>
  );
});

PasswordSettings.displayName = 'PasswordSettings';
