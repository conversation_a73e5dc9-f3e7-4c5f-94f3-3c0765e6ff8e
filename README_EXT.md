# SILLYPASS Chrome Extension

Transform SILLYPASS into a powerful Chrome extension for instant password generation across the web!

## 🚀 Quick Start

### Install as Unpacked Extension (Development)

1. **Build the extension:**
   ```bash
   npm run build:ext
   ```

2. **Open Chrome Extensions page:**
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right)

3. **Load the extension:**
   - Click "Load unpacked"
   - Select the `chrome-ext/` folder from this project
   - The SILLYPASS icon should appear in your toolbar

4. **Start using:**
   - Click the SILLYPASS icon to open the popup
   - Generate passwords and copy them instantly
   - Use keyboard shortcut `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac) to generate directly into focused input fields

## ✨ Features

### Popup Interface
- **Full SILLYPASS UI** in a convenient popup window
- **One-click generation** with customizable options
- **Instant copy** to clipboard
- **Persistent settings** saved across sessions

### Content Script Integration
- **Auto-insert passwords** into focused input fields
- **Keyboard shortcuts** for quick generation (`Ctrl+Shift+G`)
- **Visual notifications** when passwords are generated
- **Smart field detection** (password, text, textarea fields)

### Background Services
- **Settings synchronization** across Chrome instances
- **Clipboard management** with proper permissions
- **Context menu integration** (optional - can be enabled)

## 🛠️ Development

### File Structure
```
chrome-ext/
├── manifest.json          # Extension configuration (Manifest v3)
├── popup.html             # Popup window HTML
├── popup.js               # Popup React app entry point
├── background.js          # Service worker for background tasks
├── content.js             # Content script for page interaction
├── icons/                 # Extension icons (16, 32, 48, 128px)
└── dist/                  # Built React app assets (auto-generated)
```

### Build Process
The `npm run build:ext` command:
1. Runs the standard Vite build (`npm run build`)
2. Copies built assets to `chrome-ext/dist/`
3. Updates file references in popup.html and popup.js
4. Generates placeholder icons
5. Prepares the extension for loading

### Permissions Explained
- **`storage`**: Save user preferences (word count, toggles, etc.)
- **`clipboardWrite`**: Copy generated passwords to clipboard
- **`activeTab`**: Insert passwords into focused input fields
- **`<all_urls>`**: Allow content script on all websites

## 🎯 Usage Examples

### Basic Password Generation
1. Click the SILLYPASS extension icon
2. Adjust settings (word count, numbers, symbols, funny mode)
3. Click "GENERATE FUNNY PASSWORD"
4. Password is automatically copied to clipboard

### Direct Input Field Insertion
1. Focus any password or text input field on a webpage
2. Press `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac)
3. A password is generated and inserted directly
4. Visual notification confirms the action

### Settings Persistence
- All your preferences are automatically saved
- Settings sync across all Chrome instances where you're signed in
- No need to reconfigure when switching devices

## 🔧 Customization

### Enable Context Menu (Optional)
Uncomment the context menu code in `background.js` to add a right-click option:
```javascript
chrome.contextMenus.create({
  id: 'generate-sillypass',
  title: 'Generate SILLYPASS password',
  contexts: ['editable']
});
```

### Modify Keyboard Shortcuts
Edit the keyboard event listener in `content.js` to change the shortcut:
```javascript
// Change from Ctrl+Shift+G to your preferred combination
if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'G') {
```

### Adjust Popup Size
Modify the CSS in `popup.html` to change popup dimensions:
```css
body {
  width: 380px;    /* Adjust width */
  height: 600px;   /* Adjust height */
}
```

## 📦 Publishing to Chrome Web Store

1. **Prepare for production:**
   - Test thoroughly in multiple browsers
   - Optimize icons (use PNG format, proper sizes)
   - Update version in `manifest.json`

2. **Create store listing:**
   - Screenshots of the extension in action
   - Detailed description highlighting features
   - Privacy policy (if collecting any data)

3. **Submit for review:**
   - Zip the `chrome-ext/` folder
   - Upload to Chrome Web Store Developer Dashboard
   - Pay one-time $5 developer fee

## 🐛 Troubleshooting

### Extension Won't Load
- Ensure `chrome-ext/` folder contains all required files
- Check Chrome DevTools console for errors
- Verify manifest.json syntax is valid

### Popup Doesn't Open
- Check if popup.html references correct asset paths
- Ensure React app builds successfully
- Look for JavaScript errors in extension popup DevTools

### Content Script Not Working
- Verify the website allows content scripts
- Check if keyboard shortcuts conflict with site shortcuts
- Ensure proper permissions in manifest.json

### Settings Not Saving
- Confirm `storage` permission is granted
- Check Chrome storage quota limits
- Verify background script is running

## 🔒 Privacy & Security

- **No data collection**: All password generation happens locally
- **No network requests**: Extension works completely offline
- **Secure storage**: Settings stored in Chrome's secure storage API
- **No password logging**: Generated passwords are never stored or transmitted

## 📄 License

Same license as the main SILLYPASS project. See LICENSE file for details.

---

**Happy password generating! 🎉**

For issues or feature requests, please open an issue in the main repository.