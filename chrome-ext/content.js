// content.js - Content Script for SILLYPASS Chrome Extension

// Simple password generation function (duplicated from main app for content script use)
function generateSillyPassword(wordCount = 2, includeNumbers = true, includeSymbols = false, useFunny = true) {
  const funnyAdjectives = [
    'silly', 'goofy', 'wacky', 'bouncy', 'giggly', 'wobbly', 'dizzy', 'quirky',
    'zany', 'nutty', 'loopy', 'bonkers', 'kooky', 'batty', 'dotty', 'daft',
    'cheeky', 'bubbly', 'peppy', 'zippy', 'snappy', 'fizzy', 'jazzy', 'funky',
    'wiggly', 'jiggly', 'squiggly', 'ticklish', 'giggly', 'bubbly', 'wibbly', 'wobbly',
    'fluffy', 'puffy', 'scruffy', 'snuggly', 'cuddly', 'huggable', 'squeezable', 'lovable',
    'cheerful', 'playful', 'joyful', 'gleeful', 'blissful', 'delightful', 'wonderful', 'magical',
    'sparkly', 'twinkly', 'shimmery', 'glittery', 'dazzling', 'brilliant', 'radiant', 'luminous',
    'bouncy', 'springy', 'elastic', 'flexible', 'bendy', 'stretchy', 'squishy', 'smooshy',
    'perky', 'spunky', 'feisty', 'frisky', 'lively', 'spirited', 'energetic', 'dynamic',
    'whimsical', 'fanciful', 'dreamy', 'imaginative', 'creative', 'artistic', 'colorful', 'vibrant',
    'mischievous', 'sneaky', 'crafty', 'clever', 'witty', 'smart', 'brainy', 'genius',
    'adorable', 'precious', 'sweet', 'charming', 'lovely', 'beautiful', 'gorgeous', 'stunning'
  ];

  const funnyNouns = [
    'banana', 'pickle', 'noodle', 'waffle', 'muffin', 'cookie', 'pretzel', 'bagel',
    'pancake', 'donut', 'taco', 'burrito', 'pizza', 'cheese', 'butter', 'jelly',
    'penguin', 'llama', 'koala', 'panda', 'otter', 'sloth', 'hedgehog', 'ferret',
    'hamster', 'chinchilla', 'capybara', 'quokka', 'axolotl', 'platypus', 'narwhal', 'dolphin',
    'cupcake', 'brownie', 'fudge', 'truffle', 'bonbon', 'lollipop', 'gummy', 'marshmallow',
    'balloon', 'rainbow', 'cloud', 'star', 'moon', 'sun', 'flower', 'tree',
    'castle', 'bridge', 'tower', 'lighthouse', 'windmill', 'carousel', 'ferris', 'swing'
  ];
  
  const normalAdjectives = [
    'quick', 'bright', 'calm', 'bold', 'wise', 'kind', 'fair', 'true',
    'pure', 'safe', 'warm', 'cool', 'fast', 'slow', 'high', 'deep'
  ];
  
  const normalNouns = [
    'river', 'mountain', 'forest', 'ocean', 'valley', 'meadow', 'garden', 'bridge',
    'castle', 'tower', 'palace', 'temple', 'library', 'school', 'market', 'harbor'
  ];
  
  const adjectives = useFunny ? funnyAdjectives : normalAdjectives;
  const nouns = useFunny ? funnyNouns : normalNouns;
  
  const words = [];
  
  for (let i = 0; i < wordCount; i++) {
    if (i % 2 === 0) {
      words.push(adjectives[Math.floor(Math.random() * adjectives.length)]);
    } else {
      words.push(nouns[Math.floor(Math.random() * nouns.length)]);
    }
  }
  
  let password = words.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('-');
  
  if (includeNumbers) {
    password += Math.floor(Math.random() * 1000);
  }
  
  if (includeSymbols) {
    const symbols = ['!', '@', '#', '$', '%', '&', '*'];
    password += symbols[Math.floor(Math.random() * symbols.length)];
  }
  
  return password;
}

// Find the currently focused input field
function findActiveInput() {
  const activeElement = document.activeElement;
  if (activeElement && (
    activeElement.tagName === 'INPUT' && 
    (activeElement.type === 'password' || activeElement.type === 'text') ||
    activeElement.tagName === 'TEXTAREA'
  )) {
    return activeElement;
  }
  return null;
}

// Insert password into active input field
function insertPassword(password) {
  const activeInput = findActiveInput();
  if (activeInput) {
    activeInput.value = password;
    activeInput.dispatchEvent(new Event('input', { bubbles: true }));
    activeInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    // Show brief notification
    showNotification('SILLYPASS generated!');
    return true;
  }
  return false;
}

// Show temporary notification
function showNotification(message) {
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #8b5cf6;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: "IBM Plex Mono", monospace;
    font-size: 14px;
    font-weight: 600;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
  `;
  
  // Add animation keyframes
  if (!document.getElementById('sillypass-styles')) {
    const style = document.createElement('style');
    style.id = 'sillypass-styles';
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);
  }
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.remove();
  }, 2000);
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'generateAndInsert') {
    // Get settings from storage and generate password
    chrome.storage.sync.get(['wordCount', 'includeNumbers', 'includeSymbols', 'useFunny'], (settings) => {
      const password = generateSillyPassword(
        settings.wordCount || 2,
        settings.includeNumbers !== false,
        settings.includeSymbols || false,
        settings.useFunny !== false
      );
      
      const success = insertPassword(password);
      sendResponse({ success, password });
    });
    
    return true; // Keep message channel open for async response
  }
});

// Optional: Add keyboard shortcut listener
document.addEventListener('keydown', (e) => {
  // Ctrl/Cmd + Shift + G to generate password
  if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'G') {
    e.preventDefault();
    
    chrome.storage.sync.get(['wordCount', 'includeNumbers', 'includeSymbols', 'useFunny'], (settings) => {
      const password = generateSillyPassword(
        settings.wordCount || 2,
        settings.includeNumbers !== false,
        settings.includeSymbols || false,
        settings.useFunny !== false
      );
      
      insertPassword(password);
    });
  }
});