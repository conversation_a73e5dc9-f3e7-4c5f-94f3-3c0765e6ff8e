<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SillyPass Wide Popup Test</title>
    <link rel="stylesheet" href="popup.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: system-ui, -apple-system, sans-serif;
            min-height: 100vh;
        }
        
        .test-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }
        
        .test-header p {
            margin: 10px 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .popup-container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }
        
        .popup-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #fddd62, #fdd042, #fddd62);
        }
        
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .test-info strong {
            color: #495057;
        }
        
        .popup-content {
            background: hsl(var(--background));
        }
        
        .test-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            font-size: 0.85rem;
        }
        
        .test-controls h3 {
            margin: 0 0 10px 0;
            font-size: 0.9rem;
            color: #495057;
        }
        
        .test-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 2px;
            transition: background 0.2s;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .dimensions-display {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.8rem;
        }
        
        @media (max-width: 750px) {
            .test-controls {
                position: static;
                margin-bottom: 20px;
            }
            
            .dimensions-display {
                position: static;
                margin-top: 20px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🎉 SillyPass Single-Column Popup Test</h1>
        <p>Testing the new 700px × 500px single-column layout with character count control</p>
    </div>
    
    <div class="test-controls">
        <h3>Test Controls</h3>
        <button onclick="testGeneration()">Test Generation</button>
        <button onclick="testCopy()">Test Copy</button>
        <button onclick="testKeyboard()">Test Keyboard</button>
        <button onclick="testResponsive()">Test Responsive</button>
    </div>
    
    <div class="popup-container">
        <div class="test-info">
            <strong>Popup Dimensions:</strong> 700px wide × 500px tall (2.2x wider than original) |
            <strong>Layout:</strong> Single-column vertical layout |
            <strong>Features:</strong> Character count control (6-20), auto-copy, keyboard shortcuts, enhanced animations
        </div>
        
        <div class="popup-content bg-sillypass ext">
            <div id="root"></div>
        </div>
    </div>
    
    <div class="dimensions-display" id="dimensionsDisplay">
        Popup: 700×500px | Viewport: <span id="viewportSize"></span>
    </div>
    
    <script src="popup-wide.js"></script>
    <script>
        // Test functions
        function testGeneration() {
            const button = document.getElementById('generateButton');
            if (button) {
                button.click();
                console.log('✅ Generation test triggered');
            }
        }
        
        function testCopy() {
            const copyButton = document.getElementById('copyButton');
            if (copyButton && copyButton.style.display !== 'none') {
                copyButton.click();
                console.log('✅ Copy test triggered');
            } else {
                console.log('⚠️ Generate a password first');
            }
        }
        
        function testKeyboard() {
            console.log('🎹 Testing keyboard shortcuts...');
            console.log('- Press Enter or Space to generate');
            console.log('- Press Ctrl+C to copy');
            
            // Simulate Enter key
            const event = new KeyboardEvent('keydown', { key: 'Enter' });
            document.dispatchEvent(event);
        }
        
        function testResponsive() {
            const container = document.querySelector('.popup-container');
            const currentWidth = container.style.maxWidth;
            
            if (currentWidth === '400px') {
                container.style.maxWidth = '700px';
                console.log('📱 Switched to wide layout');
            } else {
                container.style.maxWidth = '400px';
                console.log('💻 Switched to narrow layout');
            }
        }
        
        // Update viewport size display
        function updateViewportSize() {
            const viewportSize = document.getElementById('viewportSize');
            if (viewportSize) {
                viewportSize.textContent = `${window.innerWidth}×${window.innerHeight}px`;
            }
        }
        
        // Initialize
        updateViewportSize();
        window.addEventListener('resize', updateViewportSize);
        
        // Log test information
        console.log('🎉 SillyPass Single-Column Popup Test Loaded');
        console.log('📏 Popup dimensions: 700×500px');
        console.log('🎯 Single-column layout with character count control (6-20 characters)');
        console.log('🎯 Use test controls to verify functionality');
        console.log('⌨️ Keyboard shortcuts: Enter/Space (generate), Ctrl+C (copy)');
    </script>
</body>
</html>
