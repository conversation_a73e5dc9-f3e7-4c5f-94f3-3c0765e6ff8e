# SILLYPASS Chrome Extension

A Chrome extension that generates funny, memorable passwords with customizable options.

## Features

- 🎭 **Funny Password Generation**: Creates memorable passwords using silly adjectives and nouns
- ⚙️ **Customizable Settings**: Control word count, numbers, symbols, and word style
- 📋 **One-Click Copy**: Instantly copy generated passwords to clipboard
- ⌨️ **Keyboard Shortcut**: Use Ctrl+Shift+G on any webpage to generate passwords directly
- 🎨 **Dark Theme**: Beautiful dark interface that matches modern design standards

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the `chrome-ext` folder
4. The SILLYPASS extension will appear in your extensions toolbar

## Usage

### Popup Interface
- Click the SILLYPASS icon in your toolbar to open the popup
- Click "Generate Password" to create a new password
- Use the "Copy" button to copy the password to your clipboard
- Adjust settings to customize password generation:
  - **Include Numbers**: Add random numbers (0-999)
  - **Include Symbols**: Add special characters (!@#$%&*)
  - **Use Funny Words**: Toggle between funny and normal word lists
  - **Word Count**: Choose 2-4 words per password

### Direct Webpage Integration
- Focus any password or text input field on a webpage
- Press `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac)
- A password will be automatically generated and inserted
- A notification will confirm the password was generated

## Example Passwords

- `Silly-Banana42` (2 words, numbers, funny)
- `Kooky-Burrito756#` (2 words, numbers, symbols, funny)
- `Quick-River-Mountain123` (3 words, numbers, normal)
- `Bouncy-Waffle-Pickle-Donut891*` (4 words, numbers, symbols, funny)

## Technical Details

### Files Structure
- `manifest.json` - Extension configuration
- `popup.html` - Popup interface HTML
- `popup.js` - Popup functionality and password generation
- `background.js` - Service worker for extension lifecycle
- `content.js` - Content script for webpage integration
- `icons/` - Extension icons (16px, 32px, 48px, 128px)

### Permissions
- `storage` - Save user preferences
- `clipboardWrite` - Copy passwords to clipboard
- `activeTab` - Insert passwords into active webpage inputs
- `<all_urls>` - Allow content script on all websites

## Recent Fixes

✅ **Service Worker Registration**: Fixed status code 15 error by commenting out unused context menu functionality
✅ **Context Menu Error**: Resolved undefined `onClicked` error in background script
✅ **Module Resolution**: Fixed React import issues by implementing vanilla JavaScript popup
✅ **Chrome Storage**: Added fallback handling for testing outside extension environment
✅ **Popup Interface**: Created fully functional standalone popup with proper styling

## Development

The extension uses vanilla JavaScript for maximum compatibility and minimal dependencies. All password generation logic is self-contained and doesn't require external libraries.

### Testing
- Load the extension in Chrome developer mode
- Test popup functionality by clicking the extension icon
- Test keyboard shortcut (Ctrl+Shift+G) on various websites
- Verify password insertion works in different input types

## Security Notes

- All password generation happens locally in the browser
- No passwords are sent to external servers
- Settings are stored locally using Chrome's storage API
- The extension only accesses the currently active tab when explicitly triggered