// background.js - Service Worker for SILLYPASS Chrome Extension

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('SILLYPASS extension installed');
  
  // Set default settings
  chrome.storage.sync.set({
    wordCount: 2,
    includeNumbers: true,
    includeSymbols: false,
    useFunny: true,
    autoCopy: false,
    darkMode: false
  });
});

// Context menu functionality (commented out until needed)
// chrome.contextMenus.onClicked.addListener((info, tab) => {
//   if (info.menuItemId === 'generate-sillypass') {
//     // Send message to content script to generate and insert password
//     chrome.tabs.sendMessage(tab.id, { action: 'generateAndInsert' });
//   }
// });

// Handle messages from popup or content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'copyToClipboard') {
    // Handle clipboard operations
    navigator.clipboard.writeText(request.text).then(() => {
      sendResponse({ success: true });
    }).catch(err => {
      sendResponse({ success: false, error: err.message });
    });
    return true; // Keep message channel open for async response
  }
});

// Optional: Create context menu item
// chrome.runtime.onInstalled.addListener(() => {
//   chrome.contextMenus.create({
//     id: 'generate-sillypass',
//     title: 'Generate SILLYPASS password',
//     contexts: ['editable']
//   });
// });