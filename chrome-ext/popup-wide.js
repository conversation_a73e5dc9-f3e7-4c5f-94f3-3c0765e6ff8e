// Wide Popup Layout for SillyPass Chrome Extension
// This creates a two-column layout optimized for the 700px wide popup

class SillyPassWidePopup {
  constructor() {
    this.currentPassword = '';
    this.characterCount = 12;
    this.includeNumbers = true;
    this.includeFunnyWords = true;
    this.isGenerating = false;
    
    // Word lists for memorable passwords
    this.wordLists = {
      funnyAdjectives: [
        'silly', 'goofy', 'wacky', 'bouncy', 'giggly', 'wobbly', 'dizzy', 'quirky',
        'zany', 'nutty', 'loopy', 'bonkers', 'kooky', 'batty', 'dotty', 'daft',
        'cheeky', 'bubbly', 'peppy', 'zippy', 'snappy', 'fizzy', 'jazzy', 'funky'
      ],
      animals: [
        'penguin', 'llama', 'koala', 'panda', 'otter', 'sloth', 'hedgehog', 'ferret',
        'hamster', 'chinchilla', 'capybara', 'quokka', 'axolotl', 'platypus', 'narwhal', 'dolphin',
        'octopus', 'jellyfish', 'seahorse', 'starfish', 'butterfly', 'ladybug', 'dragonfly', 'firefly'
      ],
      foods: [
        'waffle', 'pancake', 'muffin', 'cookie', 'pretzel', 'bagel', 'donut', 'croissant',
        'taco', 'burrito', 'pizza', 'pasta', 'noodle', 'ramen', 'sushi', 'dumpling',
        'cheese', 'butter', 'honey', 'maple', 'vanilla', 'chocolate', 'caramel', 'strawberry'
      ],
      actions: [
        'dancing', 'jumping', 'spinning', 'bouncing', 'wiggling', 'giggling', 'sneezing', 'yawning',
        'skipping', 'hopping', 'sliding', 'rolling', 'tumbling', 'flipping', 'twirling', 'prancing'
      ]
    };
    
    this.init();
  }
  
  init() {
    this.createWideLayout();
    this.bindEvents();
    this.generatePassword();
  }
  
  createWideLayout() {
    const root = document.getElementById('root');
    root.innerHTML = `
      <div class="single-column-container">
        <!-- Header -->
        <div class="single-column-header">
          <img src="logo.png" alt="SillyPass" class="sillypass-logo" style="width: 180px; height: auto; margin-bottom: 1rem;">
          <div class="sillypass-subtitle">Passwords that you'll actually remember!</div>
        </div>

        <!-- Main Content -->
        <div class="single-column-main">
          <!-- Password Generator Section -->
          <div class="single-column-generator">
            <!-- Password Display -->
            <div class="single-column-password-display" id="passwordDisplay">
              <span id="passwordText">Click generate to create a password</span>
              <button class="single-column-copy-button" id="copyButton" style="display: none;">
                <svg class="copy-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
              <div class="copy-notification" id="copyNotification">Copied!</div>
            </div>

            <!-- Generate Button -->
            <button class="single-column-button" id="generateButton">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
              </svg>
              Generate Funny Password
            </button>
          </div>

          <!-- Controls Section -->
          <div class="single-column-controls">
            <!-- Character Count -->
            <div class="single-column-controls-section">
              <div class="single-column-controls-title">Character Count</div>
              <div class="single-column-slider-container">
                <div class="single-column-slider-label">
                  <span>Characters</span>
                  <span id="characterCountValue">${this.characterCount}</span>
                </div>
                <input type="range" class="sillypass-slider" id="characterCountSlider"
                       min="6" max="20" value="${this.characterCount}"
                       style="--slider-value: ${(this.characterCount - 6) / 14 * 100}%">
              </div>
            </div>

            <!-- Options -->
            <div class="single-column-controls-section">
              <div class="single-column-controls-title">Options</div>
              <div class="single-column-toggles">
                <div class="single-column-toggle-item">
                  <span class="single-column-toggle-label">Include Numbers</span>
                  <div class="sillypass-toggle-switch ${this.includeNumbers ? 'active' : ''}" id="numbersToggle"></div>
                </div>
                <div class="single-column-toggle-item">
                  <span class="single-column-toggle-label">Use Funny Words</span>
                  <div class="sillypass-toggle-switch ${this.includeFunnyWords ? 'active' : ''}" id="funnyToggle"></div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="single-column-controls-section">
              <button class="single-column-button" id="regenerateButton" style="font-size: 0.9rem; padding: 0.75rem 1.5rem;">
                🔄 Regenerate
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }
  
  bindEvents() {
    // Generate button
    document.getElementById('generateButton').addEventListener('click', () => {
      this.generatePassword();
    });
    
    // Regenerate button
    document.getElementById('regenerateButton').addEventListener('click', () => {
      this.generatePassword();
    });
    
    // Copy button
    document.getElementById('copyButton').addEventListener('click', () => {
      this.copyPassword();
    });
    
    // Character count slider
    const slider = document.getElementById('characterCountSlider');
    slider.addEventListener('input', (e) => {
      this.characterCount = parseInt(e.target.value);
      document.getElementById('characterCountValue').textContent = this.characterCount;
      e.target.style.setProperty('--slider-value', `${(this.characterCount - 6) / 14 * 100}%`);
    });
    
    // Toggle switches
    document.getElementById('numbersToggle').addEventListener('click', () => {
      this.includeNumbers = !this.includeNumbers;
      document.getElementById('numbersToggle').classList.toggle('active', this.includeNumbers);
    });
    
    document.getElementById('funnyToggle').addEventListener('click', () => {
      this.includeFunnyWords = !this.includeFunnyWords;
      document.getElementById('funnyToggle').classList.toggle('active', this.includeFunnyWords);
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        if (e.target.tagName !== 'INPUT') {
          e.preventDefault();
          this.generatePassword();
        }
      } else if (e.ctrlKey && e.key === 'c') {
        if (this.currentPassword) {
          this.copyPassword();
        }
      }
    });
  }
  
  getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
  }
  
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  
  generatePassword() {
    if (this.isGenerating) return;

    this.isGenerating = true;
    const button = document.getElementById('generateButton');
    const regenerateButton = document.getElementById('regenerateButton');

    // Update button states
    button.innerHTML = '⏳ Generating...';
    button.disabled = true;
    regenerateButton.disabled = true;

    // Simulate generation delay for better UX
    setTimeout(() => {
      let password = this.generatePasswordWithCharacterCount();

      this.currentPassword = password;
      this.displayPassword(password);

      // Reset button states
      button.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
        </svg>
        Generate Funny Password
      `;
      button.disabled = false;
      regenerateButton.disabled = false;
      this.isGenerating = false;
    }, 300);
  }

  generatePasswordWithCharacterCount() {
    const targetLength = this.characterCount;
    let password = '';
    let attempts = 0;
    const maxAttempts = 50;

    while (password.length !== targetLength && attempts < maxAttempts) {
      attempts++;
      password = this.buildPasswordToLength(targetLength);
    }

    // If we couldn't hit exact length, pad or trim
    if (password.length !== targetLength) {
      password = this.adjustPasswordLength(password, targetLength);
    }

    return password;
  }

  buildPasswordToLength(targetLength) {
    const words = [];
    let currentLength = 0;
    let wordIndex = 0;

    // Reserve space for numbers if enabled
    const numberSpace = this.includeNumbers ? Math.floor(Math.random() * 3) + 1 : 0; // 1-3 digits
    const availableLength = targetLength - numberSpace;

    // Start with an adjective if using funny words
    if (this.includeFunnyWords && availableLength > 4) {
      const adjective = this.getRandomElement(this.wordLists.funnyAdjectives);
      const capitalizedAdj = this.capitalizeFirst(adjective);

      if (currentLength + capitalizedAdj.length <= availableLength) {
        words.push(capitalizedAdj);
        currentLength += capitalizedAdj.length;
        wordIndex++;
      }
    }

    // Add more words to fill remaining space
    const allCategories = [
      ...this.wordLists.animals,
      ...this.wordLists.foods,
      ...this.wordLists.actions
    ];

    while (currentLength < availableLength - 2) { // Leave room for separators
      const remainingSpace = availableLength - currentLength - (words.length > 0 ? 1 : 0); // Account for separator

      // Find words that fit in remaining space
      const suitableWords = allCategories.filter(word => word.length <= remainingSpace);

      if (suitableWords.length === 0) break;

      const selectedWord = this.getRandomElement(suitableWords);
      const capitalizedWord = this.capitalizeFirst(selectedWord);

      if (words.length > 0) {
        currentLength += 1; // For separator
      }

      words.push(capitalizedWord);
      currentLength += capitalizedWord.length;
      wordIndex++;

      // Prevent infinite loops
      if (wordIndex > 5) break;
    }

    // Join words with separators
    let result = words.join('-');

    // Add numbers if enabled
    if (this.includeNumbers && numberSpace > 0) {
      const maxNumber = Math.pow(10, numberSpace) - 1;
      const number = Math.floor(Math.random() * maxNumber) + 1;
      result += number.toString();
    }

    return result;
  }

  adjustPasswordLength(password, targetLength) {
    if (password.length > targetLength) {
      // Trim from the end, but try to keep it meaningful
      return password.substring(0, targetLength);
    } else if (password.length < targetLength) {
      // Pad with numbers or characters
      const deficit = targetLength - password.length;

      if (this.includeNumbers) {
        // Add random numbers
        for (let i = 0; i < deficit; i++) {
          password += Math.floor(Math.random() * 10);
        }
      } else {
        // Add random letters
        const letters = 'abcdefghijklmnopqrstuvwxyz';
        for (let i = 0; i < deficit; i++) {
          password += letters[Math.floor(Math.random() * letters.length)];
        }
      }
    }

    return password;
  }
  
  displayPassword(password) {
    const passwordText = document.getElementById('passwordText');
    const copyButton = document.getElementById('copyButton');
    const display = document.getElementById('passwordDisplay');
    
    passwordText.textContent = password;
    copyButton.style.display = 'flex';
    display.classList.add('password-fade-in');
    
    // Auto-copy to clipboard
    this.copyToClipboard(password, false);
    
    // Remove animation class after animation completes
    setTimeout(() => {
      display.classList.remove('password-fade-in');
    }, 400);
  }
  
  copyPassword() {
    if (this.currentPassword) {
      this.copyToClipboard(this.currentPassword, true);
    }
  }
  
  async copyToClipboard(text, showNotification = true) {
    try {
      await navigator.clipboard.writeText(text);
      
      if (showNotification) {
        const notification = document.getElementById('copyNotification');
        notification.classList.add('show');
        
        setTimeout(() => {
          notification.classList.remove('show');
        }, 2000);
      }
    } catch (err) {
      console.error('Failed to copy password:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }
}

// Initialize the popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SillyPassWidePopup();
});
