<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>SILLYPASS - Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="popup.css" />
    <style>
      /* Test container to simulate extension popup */
      .test-container {
        width: 700px;
        height: 500px;
        border: 2px solid #ccc;
        margin: 20px auto;
        overflow-y: auto;
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
      }

      /* Additional test styles for wide layout */
      body {
        background: #f5f5f5;
        font-family: system-ui, -apple-system, sans-serif;
        margin: 0;
        padding: 20px;
      }

      .test-info {
        text-align: center;
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="test-info">
      <strong>SillyPass Chrome Extension - Wide Popup Test</strong><br>
      Dimensions: 700px × 500px (2x wider than original)
    </div>
    <div class="test-container">
      <div class="bg-sillypass ext" style="width: 100%; height: 100%;">
        <!-- Popup content mounts here -->
        <div id="root"></div>
      </div>
    </div>

    <!-- Wide Popup script -->
    <script src="popup-wide.js"></script>
  </body>
</html>