/* Compiled Tailwind CSS from web app */
*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}

/* CSS Variables */
:root{--background: 300 20% 92%;--foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--primary: 240 5.9% 10%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--accent: 262 83% 58%;--accent-foreground: 0 0% 98%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--ring: 262 83% 58%;--radius: .5rem;--warning: 38 92% 50%;--success: 142 76% 36%;--yellow: 45 93% 58%}

/* Base styles */
*{border-color:hsl(var(--border))}
body{background-color:hsl(var(--background));color:hsl(var(--foreground));font-family:IBM Plex Mono,monospace;font-weight:400}

/* Utility classes */
.container{width:100%;margin-right:auto;margin-left:auto;padding-right:2rem;padding-left:2rem}
.mx-auto{margin-left:auto;margin-right:auto}
.mb-8{margin-bottom:2rem}
.mb-12{margin-bottom:3rem}
.mt-2{margin-top:.5rem}
.flex{display:flex}
.justify-center{justify-content:center}
.items-center{align-items:center}
.items-stretch{align-items:stretch}
.justify-between{justify-content:space-between}
.flex-col{flex-direction:column}
.gap-2{gap:.5rem}
.gap-4{gap:1rem}
.w-80{width:20rem}
.w-full{width:100%}
.h-20{height:5rem}
.max-w-md{max-width:28rem}
.py-12{padding-top:3rem;padding-bottom:3rem}
.px-4{padding-left:1rem;padding-right:1rem}
.p-2{padding:.5rem}
.ml-4{margin-left:1rem}
.text-center{text-align:center}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.break-all{word-break:break-all}
.relative{position:relative}
.absolute{position:absolute}
.top-0{top:0}
.right-0{right:0}
.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.-translate-y-2{--tw-translate-y: -.5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-x-2{--tw-translate-x: .5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.bg-purple-600{--tw-bg-opacity: 1;background-color:rgb(147 51 234 / var(--tw-bg-opacity, 1))}
.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}
.text-green-600{--tw-text-opacity: 1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}
.text-gray-500{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}
.px-3{padding-left:.75rem;padding-right:.75rem}
.py-1{padding-top:.25rem;padding-bottom:.25rem}
.rounded-full{border-radius:9999px}
.text-sm{font-size:.875rem;line-height:1.25rem}
.font-medium{font-weight:500}
.animate-bounce{animation:bounce 1s infinite}
.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}
.hover\:bg-gray-100:hover{--tw-bg-opacity: 1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}
.rounded-lg{border-radius:var(--radius)}
.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}

/* SILLYPASS specific styles */
.bg-sillypass{background-color:hsl(var(--background))}
.sillypass-subtitle{font-family:IBM Plex Mono,monospace;font-size:1.125rem;font-weight:400;color:hsl(var(--foreground));text-align:center;margin-top:1rem}
.sillypass-password-field{background:#fff;border:2px solid hsl(var(--border));border-radius:1rem;padding:1.5rem 2rem;font-family:IBM Plex Mono,monospace;font-size:1.25rem;font-weight:600;color:hsl(var(--foreground));text-align:center;box-shadow:0 2px 8px #0000001a;margin:2rem 0 1rem 0}
.sillypass-button{background:#fddd62;color:#000;font-family:IBM Plex Mono,monospace;font-size:1.125rem;font-weight:700;text-transform:uppercase;letter-spacing:.025em;border:.74px solid #000000;border-radius:.75rem;padding:1rem 4rem;width:100%;cursor:pointer;transition:all .2s ease;margin:0.5rem 0;display:inline-flex;justify-content:center;align-items:center;gap:.5rem}
.sillypass-button:hover{background:#fdd042;transform:translateY(-2px) scale(1.02);box-shadow:0 6px 16px rgba(0,0,0,0.15);transition:all 0.2s cubic-bezier(0.4, 0, 0.2, 1)}
.sillypass-button:active{transform:translateY(0) scale(0.95);box-shadow:0 2px 8px rgba(0,0,0,0.1)}
.sillypass-button:focus-visible{outline:2px solid #8B5CF6;outline-offset:2px;box-shadow:0 0 0 4px rgba(139, 92, 246, 0.2)}
.sillypass-controls{display:flex;flex-direction:column;gap:2rem;margin-top:2rem}
.sillypass-word-count{display:flex;flex-direction:column;align-items:center;gap:1rem}
.sillypass-word-count-label{font-family:IBM Plex Mono,monospace;font-size:1.125rem;font-weight:600;color:hsl(var(--foreground))}
.sillypass-slider{width:100%;height:8px;background:hsl(var(--muted));border-radius:4px;outline:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}
.sillypass-slider::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:24px;height:24px;background:hsl(var(--accent));border-radius:50%;cursor:pointer;box-shadow:0 2px 4px #0003}
.sillypass-slider::-moz-range-thumb{width:24px;height:24px;background:hsl(var(--accent));border-radius:50%;cursor:pointer;border:none;box-shadow:0 2px 4px #0003}
.sillypass-toggles{display:flex;justify-content:space-between;align-items:center;gap:2rem}
.sillypass-toggle-item{display:flex;flex-direction:column;align-items:center;gap:.5rem}
.sillypass-toggle-label{font-family:IBM Plex Mono,monospace;font-size:1rem;font-weight:600;color:hsl(var(--foreground))}
.sillypass-toggle-switch{width:48px;height:24px;background:hsl(var(--muted));border-radius:12px;position:relative;cursor:pointer;transition:all .2s ease}
.sillypass-toggle-switch.active{background:hsl(var(--accent))}
.sillypass-toggle-switch:after{content:"";position:absolute;top:2px;left:2px;width:20px;height:20px;background:#fff;border-radius:50%;transition:all .2s ease;box-shadow:0 1px 3px #0003}
.sillypass-toggle-switch.active:after{transform:translate(24px)}

/* Animations */
@keyframes slideIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}
.slide-in{animation:slideIn .5s ease-out}
@keyframes bounce{0%,to{transform:translateY(-25%);animation-timing-function:cubic-bezier(.8,0,1,1)}50%{transform:none;animation-timing-function:cubic-bezier(0,0,.2,1)}}

/* Extension specific adjustments */
body {
  width: 500px;
  height: 600px;
  overflow-y: auto;
  margin: 0;
  min-width: 500px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--accent));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--accent));
  opacity: 0.8;
}
/* Extension-specific improvements */

/* Logo styling */
.sillypass-logo {
  width: 18vw !important;
  height: auto !important;
  display: block !important;
  margin: 0 auto !important;
  margin-bottom: 1.5rem !important;
}

/* Updated button with 2px border */
.sillypass-button {
  border: 2px solid #000000 !important;
  border-radius: 12px !important;
}

.sillypass-button:hover {
  border-color: #333 !important;
  box-shadow: 0 4px 8px #0000001a !important;
}

/* Updated password field with 2px border */
.sillypass-password-field {
  border: 2px solid #000000 !important;
  font-weight: 500 !important;
}

/* Extension-specific typography scaling */
body.ext .sillypass-subtitle {
  font-size: 0.95rem !important;
}

body.ext .sillypass-password-field {
  font-size: 1rem !important;
}

body.ext .sillypass-button {
  font-size: 0.875rem !important;
}

body.ext .sillypass-toggle-label {
  font-size: 0.875rem !important;
}

body.ext .container {
  max-width: 500px !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Continuous slider improvements */
.sillypass-slider {
  position: relative;
  background: linear-gradient(to right, hsl(var(--accent)) var(--slider-value, 50%), hsl(var(--muted)) var(--slider-value, 50%)) !important;
}

.sillypass-slider::-webkit-slider-thumb {
  width: 20px !important;
  height: 20px !important;
  border: 2px solid #fff !important;
  position: relative;
  z-index: 2;
}

.sillypass-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 8px hsl(var(--accent) / 0.1);
}

.sillypass-slider::-webkit-slider-thumb:focus {
  box-shadow: 0 0 0 8px hsl(var(--accent) / 0.2);
}

/* Enhanced copy button micro-interactions */
.copy-btn {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.copy-btn:hover {
  transform: scale(1.05) translateY(-1px);
  background: hsl(var(--muted));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.copy-btn:active {
  transform: scale(0.95) translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.copy-btn:focus-visible {
  outline: 2px solid hsl(var(--accent));
  outline-offset: 2px;
}

.copy-icon {
  width: 20px;
  height: 20px;
  color: hsl(var(--muted-foreground));
  transition: all 0.2s ease;
}

.copy-icon.success {
  color: hsl(var(--success));
}

/* Copy notification */
.copy-notification {
  position: absolute;
  top: -12px;
  right: -8px;
  background: hsl(var(--accent));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(8px);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.copy-notification.show {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced password field animations */
.password-fade-in {
  animation: fadeInUp 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.password-generating {
  animation: passwordPulse 0.6s ease-out, shimmer 1.5s ease-in-out infinite;
}

.password-success {
  animation: successBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes passwordPulse {
  0% { transform: scale(1); }
  25% { transform: scale(1.02); }
  50% { transform: scale(1.05); }
  75% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes successBounce {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(0.95); }
  75% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Wide popup layout improvements */
.wide-popup-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
}

.wide-popup-header {
  text-align: center;
  margin-bottom: 1rem;
}

.wide-popup-main {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: 2rem;
  align-items: start;
}

.wide-popup-generator {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.wide-popup-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.wide-popup-password-display {
  background: #fff;
  border: 2px solid #000000;
  border-radius: 12px;
  padding: 1.5rem;
  font-family: IBM Plex Mono, monospace;
  font-size: 1.1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
}

.wide-popup-button {
  background: #fddd62;
  color: #000;
  font-family: IBM Plex Mono, monospace;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: 2px solid #000000;
  border-radius: 12px;
  padding: 1rem 2rem;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.wide-popup-button:hover {
  background: #fdd042;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.wide-popup-button:active {
  transform: translateY(0) scale(0.98);
}

.wide-popup-controls-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.wide-popup-controls-title {
  font-family: IBM Plex Mono, monospace;
  font-size: 0.9rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.wide-popup-slider-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.wide-popup-slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: IBM Plex Mono, monospace;
  font-size: 0.85rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.wide-popup-toggles {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.wide-popup-toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wide-popup-toggle-label {
  font-family: IBM Plex Mono, monospace;
  font-size: 0.85rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

/* Responsive adjustments for smaller screens */
@media (max-width: 750px) {
  .wide-popup-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .wide-popup-controls {
    order: -1;
  }
}

/* Enhanced copy button for wide layout */
.wide-copy-button {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wide-copy-button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-50%) scale(1.1);
}

.wide-copy-button:active {
  transform: translateY(-50%) scale(0.95);
}

/* Logo adjustments for wide layout */
body.ext .sillypass-logo {
  width: 200px !important;
  margin-bottom: 0.5rem !important;
}

/* Improved password display for wide layout */
.wide-popup-password-display {
  transition: all 0.3s ease;
  cursor: text;
  user-select: all;
}

.wide-popup-password-display:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.wide-popup-password-display:focus-within {
  outline: 2px solid hsl(var(--accent));
  outline-offset: 2px;
}

/* Enhanced button animations */
.wide-popup-button {
  position: relative;
  overflow: hidden;
}

.wide-popup-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.wide-popup-button:hover::before {
  left: 100%;
}

/* Improved slider styling for wide layout */
.wide-popup-slider-container .sillypass-slider {
  height: 6px !important;
  background: linear-gradient(to right, hsl(var(--accent)) var(--slider-value, 60%), hsl(var(--muted)) var(--slider-value, 60%)) !important;
}

/* Better toggle switches */
.wide-popup-toggles .sillypass-toggle-switch {
  flex-shrink: 0;
}

/* Accessibility improvements */
.wide-popup-button:focus,
.sillypass-toggle-switch:focus,
.sillypass-slider:focus {
  outline: 2px solid hsl(var(--accent));
  outline-offset: 2px;
}

/* Loading state improvements */
.wide-popup-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.wide-popup-button:disabled:hover {
  background: #fddd62 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive text sizing */
@media (max-width: 750px) {
  .wide-popup-password-display {
    font-size: 1rem !important;
    padding: 1.25rem !important;
  }

  .wide-popup-button {
    font-size: 0.9rem !important;
    padding: 0.875rem 1.5rem !important;
  }

  .wide-popup-controls {
    padding: 1.25rem !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .wide-popup-password-display {
    border-width: 3px !important;
  }

  .wide-popup-button {
    border-width: 3px !important;
  }

  .wide-popup-controls {
    border-width: 2px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .wide-popup-button,
  .wide-copy-button,
  .wide-popup-password-display,
  .copy-notification {
    transition: none !important;
    animation: none !important;
  }

  .wide-popup-button::before {
    display: none;
  }
}

/* Single Column Layout Styles */
.single-column-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  min-height: 450px;
}

.single-column-header {
  text-align: center;
  margin-bottom: 1rem;
}

.single-column-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 1;
}

.single-column-generator {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.single-column-password-display {
  background: #fff;
  border: 2px solid #000000;
  border-radius: 12px;
  padding: 1.5rem;
  font-family: IBM Plex Mono, monospace;
  font-size: 1.1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
  transition: all 0.3s ease;
}

.single-column-password-display:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.single-column-copy-button {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.single-column-copy-button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-50%) scale(1.1);
}

.single-column-copy-button:active {
  transform: translateY(-50%) scale(0.95);
}

.single-column-button {
  background: #fddd62;
  color: #000;
  font-family: IBM Plex Mono, monospace;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: 2px solid #000000;
  border-radius: 12px;
  padding: 1rem 2rem;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.single-column-button:hover {
  background: #fdd042;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.single-column-button:active {
  transform: translateY(0) scale(0.98);
}

.single-column-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.single-column-button:disabled:hover {
  background: #fddd62 !important;
  transform: none !important;
  box-shadow: none !important;
}

.single-column-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.single-column-button:hover::before {
  left: 100%;
}

.single-column-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
}

.single-column-controls-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.single-column-controls-title {
  font-family: IBM Plex Mono, monospace;
  font-size: 0.9rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.single-column-slider-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.single-column-slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: IBM Plex Mono, monospace;
  font-size: 0.85rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.single-column-toggles {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.single-column-toggle-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
}

.single-column-toggle-label {
  font-family: IBM Plex Mono, monospace;
  font-size: 0.85rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

/* Responsive adjustments for single column */
@media (max-width: 750px) {
  .single-column-container {
    padding: 1rem;
    gap: 1rem;
  }

  .single-column-password-display {
    font-size: 1rem !important;
    padding: 1.25rem !important;
  }

  .single-column-button {
    font-size: 0.9rem !important;
    padding: 0.875rem 1.5rem !important;
  }

  .single-column-controls {
    padding: 1.25rem !important;
  }

  .single-column-toggles {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Enhanced accessibility for single column */
.single-column-button:focus,
.single-column-copy-button:focus {
  outline: 2px solid hsl(var(--accent));
  outline-offset: 2px;
}

/* Reduced motion support for single column */
@media (prefers-reduced-motion: reduce) {
  .single-column-button,
  .single-column-copy-button,
  .single-column-password-display {
    transition: none !important;
    animation: none !important;
  }

  .single-column-button::before {
    display: none;
  }
}