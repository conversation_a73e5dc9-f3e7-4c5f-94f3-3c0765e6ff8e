<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SILLYPASS - Test Implementation</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* SILLYPASS Color System */
        :root {
            --background: 240 10% 98%;
            --foreground: 240 10% 3.9%;
            --card: 0 0% 100%;
            --card-foreground: 240 10% 3.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 240 10% 3.9%;
            --primary: 240 5.9% 10%;
            --primary-foreground: 0 0% 98%;
            --secondary: 240 4.8% 95.9%;
            --secondary-foreground: 240 5.9% 10%;
            --muted: 240 4.8% 95.9%;
            --muted-foreground: 240 3.8% 46.1%;
            --accent: 262 83% 58%;
            --accent-foreground: 0 0% 98%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 0 0% 98%;
            --border: 240 5.9% 90%;
            --input: 240 5.9% 90%;
            --ring: 262 83% 58%;
            --radius: 0.5rem;
            --warning: 38 92% 50%;
            --success: 142 76% 36%;
        }

        /* SILLYPASS Specific Styles */
        .bg-sillypass-gradient {
            background: linear-gradient(135deg, hsl(262 83% 95%) 0%, hsl(262 83% 90%) 100%);
        }

        .sillypass-title {
            font-size: 3rem;
            font-weight: 900;
            letter-spacing: -0.05em;
            line-height: 1;
        }

        .sillypass-subtitle {
            font-size: 1.125rem;
            font-weight: 500;
        }

        .sillypass-button {
            background: #FDDD62;
            color: #000000;
            font-family: 'IBM Plex Mono', monospace;
            font-weight: 700;
            font-size: 1.125rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            border: 0.74px solid #000000;
            border-radius: 0.75rem;
            transition: all 0.2s ease;
            cursor: pointer;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
        }

        .sillypass-button:hover {
            background: #FDD042;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .sillypass-password-field {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid hsl(var(--border));
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sillypass-controls {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid hsl(var(--border));
            border-radius: 1rem;
            padding: 1.5rem;
        }

        .btn-bounce:active {
            transform: scale(0.95);
        }

        .password-reveal {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Character Icon Component
        const CharacterIcon = ({ size = 64, className = "" }) => (
            <svg
                width={size}
                height={size}
                viewBox="0 0 100 100"
                className={className}
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                {/* Head */}
                <circle cx="50" cy="35" r="20" fill="currentColor" opacity="0.8" />
                
                {/* Eyes */}
                <circle cx="44" cy="30" r="2" fill="white" />
                <circle cx="56" cy="30" r="2" fill="white" />
                
                {/* Smile */}
                <path
                    d="M 42 38 Q 50 44 58 38"
                    stroke="white"
                    strokeWidth="2"
                    fill="none"
                    strokeLinecap="round"
                />
                
                {/* Body */}
                <ellipse cx="50" cy="65" rx="15" ry="20" fill="currentColor" opacity="0.6" />
                
                {/* Arms */}
                <ellipse cx="30" cy="60" rx="8" ry="15" fill="currentColor" opacity="0.6" transform="rotate(-30 30 60)" />
                <ellipse cx="70" cy="60" rx="8" ry="15" fill="currentColor" opacity="0.6" transform="rotate(30 70 60)" />
                
                {/* Thumbs up gesture */}
                <circle cx="25" cy="50" r="3" fill="currentColor" opacity="0.8" />
                <rect x="23" y="47" width="4" height="8" fill="currentColor" opacity="0.8" rx="2" />
            </svg>
        );

        // Character-length based password generator
        const generatePassword = (charCount = 12, includeNumbers = true, includeSymbols = false, useFunny = true) => {
            const funnyAdjectives = [
                'silly', 'goofy', 'wacky', 'bouncy', 'giggly', 'wobbly', 'dizzy', 'quirky',
                'zany', 'nutty', 'loopy', 'bonkers', 'kooky', 'batty', 'dotty', 'daft'
            ];
            
            const funnyNouns = [
                'banana', 'pickle', 'noodle', 'waffle', 'muffin', 'cookie', 'pretzel', 'bagel',
                'pancake', 'donut', 'taco', 'burrito', 'pizza', 'cheese', 'butter', 'jelly'
            ];
            
            const normalAdjectives = [
                'quick', 'bright', 'calm', 'bold', 'wise', 'kind', 'fair', 'true',
                'pure', 'safe', 'warm', 'cool', 'fast', 'slow', 'high', 'deep'
            ];
            
            const normalNouns = [
                'river', 'mountain', 'forest', 'ocean', 'valley', 'meadow', 'garden', 'bridge',
                'castle', 'tower', 'palace', 'temple', 'library', 'school', 'market', 'harbor'
            ];
            
            const adjectives = useFunny ? funnyAdjectives : normalAdjectives;
            const nouns = useFunny ? funnyNouns : normalNouns;
            
            const words = [];
            let currentLength = 0;
            let wordIndex = 0;
            
            // Build words until we approach the character limit
            while (true) {
                const isAdjective = wordIndex % 2 === 0;
                const wordArray = isAdjective ? adjectives : nouns;
                const word = wordArray[Math.floor(Math.random() * wordArray.length)];
                const capitalizedWord = word.charAt(0).toUpperCase() + word.slice(1);
                
                // Calculate length if we add this word (including hyphen if not first word)
                const additionalLength = capitalizedWord.length + (words.length > 0 ? 1 : 0);
                
                // Stop if adding this word would make us too close to the limit (leave room for fillers)
                if (currentLength + additionalLength > charCount - 3) {
                    break;
                }
                
                words.push(capitalizedWord);
                currentLength += additionalLength;
                wordIndex++;
                
                // Ensure we have at least one word
                if (words.length >= 1 && currentLength >= charCount * 0.6) {
                    break;
                }
            }
            
            // Join words with hyphens
            let password = words.join('-');
            let remaining = charCount - password.length;
            
            // Fill remaining characters
            const fillers = [];
            
            // Add numbers if enabled and we have space
            if (includeNumbers && remaining > 0) {
                const numberCount = Math.min(remaining, Math.floor(Math.random() * 3) + 1);
                for (let i = 0; i < numberCount; i++) {
                    fillers.push(Math.floor(Math.random() * 10).toString());
                }
                remaining -= numberCount;
            }
            
            // Add symbols if enabled and we have space
            if (includeSymbols && remaining > 0) {
                const symbols = ['!', '@', '#', '$', '%', '&', '*'];
                const symbolCount = Math.min(remaining, Math.floor(Math.random() * 2) + 1);
                for (let i = 0; i < symbolCount; i++) {
                    fillers.push(symbols[Math.floor(Math.random() * symbols.length)]);
                }
                remaining -= symbolCount;
            }
            
            // Fill any remaining space with random lowercase letters
            const letters = 'abcdefghijklmnopqrstuvwxyz';
            while (remaining > 0) {
                fillers.push(letters[Math.floor(Math.random() * letters.length)]);
                remaining--;
            }
            
            // Shuffle fillers and append to password
            for (let i = fillers.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [fillers[i], fillers[j]] = [fillers[j], fillers[i]];
            }
            
            password += fillers.join('');
            
            // Ensure exact length (trim if somehow over)
            return password.substring(0, charCount);
        };

        // Main SILLYPASS Component
        const SillyPassApp = () => {
            const [password, setPassword] = useState('');
            const [isGenerating, setIsGenerating] = useState(false);
            const [copySuccess, setCopySuccess] = useState(false);

            const handleGenerate = () => {
                setIsGenerating(true);
                setTimeout(() => {
                    setPassword(generatePassword(12, true, false, true));
                    setIsGenerating(false);
                }, 500);
            };

            const copyToClipboard = (text) => {
                navigator.clipboard.writeText(text).then(() => {
                    setCopySuccess(true);
                    setTimeout(() => setCopySuccess(false), 2000);
                });
            };

            return (
                <div className="min-h-screen bg-sillypass-gradient">
                    {/* Header */}
                    <div className="text-center py-12 px-4">
                        <div className="max-w-md mx-auto">
                            {/* Logo */}
                            <div className="mb-6">
                                <div className="mx-auto" style={{width: '320px', height: '80px'}}>
                                    <svg width="320" height="80" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <text x="0" y="56" fontFamily="Arial Black, sans-serif" fontSize="48" fontWeight="900" fill="#000000">SILLY</text>
                                        <defs>
                                            <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style={{stopColor:'#8B5CF6', stopOpacity:1}} />
                                                <stop offset="100%" style={{stopColor:'#A855F7', stopOpacity:1}} />
                                            </linearGradient>
                                        </defs>
                                        <text x="160" y="56" fontFamily="Arial Black, sans-serif" fontSize="48" fontWeight="900" fill="url(#purpleGradient)">PASS</text>
                                    </svg>
                                </div>
                            </div>
                            
                            {/* Subtitle */}
                            <p className="sillypass-subtitle text-gray-600 mb-2">
                                Passwords that you'll actually remember!
                            </p>
                        </div>
                    </div>

                    {/* Main Content */}
                    <div className="max-w-md mx-auto px-4 pb-8">
                        <div className="space-y-6">
                            {/* Password Display */}
                            {password && (
                                <div className="password-reveal">
                                    <div className="sillypass-password-field relative">
                                        <div className="flex items-center justify-between gap-4">
                                            <p className="font-mono text-lg font-semibold text-gray-800 break-all flex-1">
                                                {password}
                                            </p>
                                            <button
                                                onClick={() => copyToClipboard(password)}
                                                className="p-2 rounded-lg hover:bg-gray-200 transition-all duration-200 active:scale-95 min-w-[40px] min-h-[40px] flex items-center justify-center"
                                            >
                                                {copySuccess ? (
                                                    <span style={{color: 'hsl(262 83% 58%)'}}>✓</span>
                                                ) : (
                                                    <span className="text-gray-500">📋</span>
                                                )}
                                            </button>
                                        </div>
                                        {copySuccess && (
                                            <div className="absolute -top-2 -right-2 bg-purple-600 text-white px-3 py-1.5 rounded-full text-sm font-medium animate-bounce shadow-lg">
                                                Copied! ✨
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Generate Button */}
                            <button
                                onClick={handleGenerate}
                                disabled={isGenerating}
                                className="sillypass-button w-full h-16 btn-bounce"
                            >
                                {isGenerating ? (
                                    <span>GENERATING...</span>
                                ) : (
                                    <span>GENERATE FUNNY PASSWORD</span>
                                )}
                            </button>

                            {/* Controls */}
                            <div className="sillypass-controls">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">Settings</h3>
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-700">Use Humor Words</span>
                                        <div className="w-12 h-6 bg-purple-600 rounded-full relative">
                                            <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                                        </div>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-700">Length</span>
                                            <span className="text-gray-600">12 Characters</span>
                                        </div>
                                        <div className="w-full h-2 bg-gray-200 rounded-full">
                                            <div className="w-3/4 h-full bg-purple-600 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* First-time hint */}
                            {!password && (
                                <div className="text-center mt-8">
                                    <div className="inline-flex items-center gap-3 px-5 py-3 bg-white/40 backdrop-blur-sm border border-gray-300 rounded-full text-sm text-gray-600 font-medium animate-bounce shadow-sm">
                                        <span className="text-lg">👆</span>
                                        <span>Tap to generate your first memorable password</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        };

        // Render the app
        ReactDOM.render(<SillyPassApp />, document.getElementById('root'));
    </script>
</body>
</html>